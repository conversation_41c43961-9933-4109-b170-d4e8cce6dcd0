import { defineManifestConfig } from '@uni-helper/vite-plugin-uni-manifest'

export default defineManifestConfig({
  'name': '海尔绿能运维',
  'appid': '__UNI__668B718',
  'description': '',
  'versionName': '1.0.0',
  'versionCode': '100',
  'transformPx': false,
  'mp-weixin': {
    appid: '',
    setting: {
      urlCheck: false,
      es6: true,
      minified: true,
      postcss: true,
      ignoreDevUnusedFiles: false,
      ignoreUploadUnusedFiles: false,
    },
    usingComponents: true,
    lazyCodeLoading: 'requiredComponents',
    darkmode: true,
    themeLocation: 'theme.json',
    libVersion: '3.4.3',
  },
  /* 5+App特有相关 */
  'app-plus': {
    runmode: 'liberate',
    usingComponents: true,
    nvueStyleCompiler: 'uni-app',
    compilerVersion: 3,
    splashscreen: {
      alwaysShowBeforeRender: true,
      waiting: true,
      autoclose: true,
      delay: 0,
    },
    /* 模块配置 */
    modules: {
      /* 使用Canvas模块，需要添加下面这一行 */
      Canvas: 'nvue canvas',
      Camera: {},
      VideoPlayer: {},
      Maps: {},
    },
    /* 应用发布信息 */
    distribute: {
      /* android打包配置 */
      android: {
        permissions: [
          // '<uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>',
          // '<uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>',
          // '<uses-permission android:name="android.permission.VIBRATE"/>',
          // '<uses-permission android:name="android.permission.READ_LOGS"/>',
          // '<uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>',
          '<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>',
          '<uses-permission android:name="android.permission.CAMERA"/>',
          // '<uses-permission android:name="android.permission.GET_ACCOUNTS"/>',
          '<uses-permission android:name="android.permission.READ_PHONE_STATE"/>',
          // '<uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>',
          // '<uses-permission android:name="android.permission.WAKE_LOCK"/>',
          // '<uses-permission android:name="android.permission.WRITE_SETTINGS"/>',
          '<uses-permission android:name="android.permission.INTERNET" />',
        ],
      },
      /* ios打包配置 */
      ios: {
        idfa: false,
        privacyDescription: {
          NSLocationWhenInUseUsageDescription:
            '要使用您的定位来用来获取当地天气，制作水印，校准电站位置',
          NSLocationAlwaysUsageDescription:
            '要使用您的定位来用来获取当地天气，制作水印，校准电站位置',
          NSLocationAlwaysAndWhenInUseUsageDescription:
            '要使用您的定位来用来获取当地天气，制作水印，校准电站位置',
          NSPhotoLibraryUsageDescription:
            '我们的应用程序需要访问您的照片库，以便您可以选择图片进行上传，用于分享或者提报工单',
          NSCameraUsageDescription: '将要使用您的相机，用来拍摄运维过程中所需的图片',
        },
      },
      /* SDK配置 */
      sdkConfigs: {
        maps: {
          amap: {
            name: 'amap9A3wgTEN',
            appkey_ios: '8bf6ab662628c0544e2056636a949f8c',
            appkey_android: '1a952d02b0fceeba54c87dc94e558cb3',
          },
        },
      },
      useragent_android: {
        value: 'NH_APP_OSP/ANDROID', // 可选，字符串类型，设置的默认userAgent值
        concatenate: true, // 可选，Boolean类型，是否将value值作为追加值连接到系统默认userAgent值之后
      },
      useragent_ios: {
        value: 'NH_APP_OSP/IOS', // 可选，字符串类型，设置的默认userAgent值
        concatenate: true, // 可选，Boolean类型，是否将value值作为追加值连接到系统默认userAgent值之后
      },
      icons: {
        android: {
          hdpi: 'unpackage/res/icons/72x72.png',
          xhdpi: 'unpackage/res/icons/96x96.png',
          xxhdpi: 'unpackage/res/icons/144x144.png',
          xxxhdpi: 'unpackage/res/icons/192x192.png',
        },
        ios: {
          appstore: 'unpackage/res/icons/1024x1024.png',
          ipad: {
            'app': 'unpackage/res/icons/76x76.png',
            'app@2x': 'unpackage/res/icons/152x152.png',
            'notification': 'unpackage/res/icons/20x20.png',
            'notification@2x': 'unpackage/res/icons/40x40.png',
            'proapp@2x': 'unpackage/res/icons/167x167.png',
            'settings': 'unpackage/res/icons/29x29.png',
            'settings@2x': 'unpackage/res/icons/58x58.png',
            'spotlight': 'unpackage/res/icons/40x40.png',
            'spotlight@2x': 'unpackage/res/icons/80x80.png',
          },
          iphone: {
            'app@2x': 'unpackage/res/icons/120x120.png',
            'app@3x': 'unpackage/res/icons/180x180.png',
            'notification@2x': 'unpackage/res/icons/40x40.png',
            'notification@3x': 'unpackage/res/icons/60x60.png',
            'settings@2x': 'unpackage/res/icons/58x58.png',
            'settings@3x': 'unpackage/res/icons/87x87.png',
            'spotlight@2x': 'unpackage/res/icons/80x80.png',
            'spotlight@3x': 'unpackage/res/icons/120x120.png',
          },
        },
      },
    },
  },
  /* 快应用特有相关 */
  'quickapp': {},
  /* 小程序特有相关 */
  'mp-alipay': {
    usingComponents: true,
  },
  'mp-baidu': {
    usingComponents: true,
  },
  'mp-toutiao': {
    usingComponents: true,
  },
  'h5': {
    darkmode: true,
    themeLocation: 'theme.json',
  },
  'uniStatistics': {
    enable: false,
  },
  'vueVersion': '3',
})
