<script lang="ts" setup>
import type { VersionInformation } from '@/types/api/App'
import { getVersionList } from '@/api/common'
import { useAuthStore } from '@/store'
import { getSystemInfo } from '@uni-helper/uni-promises'
import dayjs from 'dayjs'
import { computed, defineExpose, onMounted, ref, watch } from 'vue'
import { useToast } from 'wot-design-uni'

const props = withDefaults(defineProps<{
  autoCheck?: boolean
}>(), {
  autoCheck: true,
})

type Download = typeof plus.downloader.Download

const showUpdatePopup = ref(false)
const latestVersionInfo = ref<VersionInformation | null>(null)
const updateButtonText = ref('立即更新')
const downloadTask = ref<Download | null>(null)
const systemInfo = ref<UniApp.GetSystemInfoResult | null>(null)
const toast = useToast()

const LAST_CHECK_KEY = 'LAST_VERSION_CHECK_TIME'

function getLastCheckTime(): string | null {
  return uni.getStorageSync(LAST_CHECK_KEY)
}

function setLastCheckTime() {
  uni.setStorageSync(LAST_CHECK_KEY, dayjs().format('YYYY-MM-DD'))
}

function shouldShowUpdate(): boolean {
  const lastCheck = getLastCheckTime()
  const today = dayjs().format('YYYY-MM-DD')
  return !lastCheck || lastCheck !== today
}

const authStore = useAuthStore()

const isForceUpdate = computed(() => latestVersionInfo.value?.isStrongUpdates === 0)

function isNewerVersion(newVersion: string, oldVersion: string): boolean {
  if (!newVersion || !oldVersion)
    return false
  const newParts = newVersion.split('.').map(Number)
  const oldParts = oldVersion.split('.').map(Number)
  const maxLength = Math.max(newParts.length, oldParts.length)
  for (let i = 0; i < maxLength; i++) {
    const newPart = newParts[i] || 0
    const oldPart = oldParts[i] || 0
    if (newPart > oldPart)
      return true
    if (newPart < oldPart)
      return false
  }
  return false
}

async function handleUpdate() {
  // #ifdef APP-PLUS
  if (!systemInfo.value || !latestVersionInfo.value)
    return

  const platform = systemInfo.value.platform

  if (platform === 'ios') {
    const appStoreUrl = 'itms-apps://itunes.apple.com/cn/app/com.haier.energy.osp/id1671970830'
    plus.runtime.openURL(appStoreUrl)
    return
  }

  if (platform === 'android') {
    if (updateButtonText.value === '下载完成，点击安装') {
      if (downloadTask.value && downloadTask.value.filename) {
        plus.runtime.install(downloadTask.value.filename)
      }
      return
    }

    if (updateButtonText.value !== '立即下载') {
      return
    }

    updateButtonText.value = '准备下载...'
    downloadTask.value = plus.downloader.createDownload(latestVersionInfo.value!.apkUrl, {
      filename: `_doc/update/app_${dayjs().format('YYYYMMDDHHmmss')}.apk`,
    })

    if (!downloadTask.value) {
      uni.showToast({ title: '创建下载任务失败', icon: 'error' })
      updateButtonText.value = '立即下载'
      return
    }

    downloadTask.value.addEventListener('statechanged', (task: Download, status: number) => {
      if (!task) {
        return
      }
      switch (task.state) {
        case 1:
          updateButtonText.value = '开始下载'
          break
        case 2:
        case 3:
          if (task.totalSize && task.totalSize > 0) {
            const progress = Math.floor((task.downloadedSize! / task.totalSize!) * 100)
            updateButtonText.value = `已下载${progress}%`
          }
          else {
            updateButtonText.value = '下载中...'
          }
          break
        case 4:
          if (status === 200) {
            updateButtonText.value = '下载完成，点击安装'
            plus.runtime.install(task.filename!)
          }
          else {
            uni.showToast({ title: '下载失败', icon: 'error' })
            updateButtonText.value = '立即下载'
            downloadTask.value = null
          }
          break
      }
    })
    downloadTask.value.start()
  }
  // #endif
}

function onPopupClose() {
  if (downloadTask.value) {
    downloadTask.value.abort()
  }
  showUpdatePopup.value = false
}

async function checkVersion(manualCheck = false): Promise<boolean> {
  if (!authStore.token) {
    if (manualCheck) {
      toast.show({ msg: '请先登录' })
    }
    return false
  }

  try {
    if (manualCheck) {
      uni.showLoading({ title: '正在检查更新...' })
    }
    systemInfo.value = await getSystemInfo()
    const versionResult = await getVersionList({
      platformCode: systemInfo.value.AppPlatform?.toUpperCase() || 'ANDROID',
      versionType: import.meta.env.VITE_APP_VERSION_TYPE,
    })

    if (versionResult && versionResult.length > 0) {
      const latestVer = versionResult[0]
      const currentVersion = systemInfo.value.appVersion
      if (currentVersion && isNewerVersion(latestVer.versionName, currentVersion)) {
        latestVersionInfo.value = latestVer
        updateButtonText.value = systemInfo.value.platform === 'ios' ? '前往应用商店' : '立即下载'

        // 在自动检查模式下，检查是否应该显示更新提示
        if (!manualCheck && !shouldShowUpdate()) {
          return true
        }

        showUpdatePopup.value = true
        if (!manualCheck) {
          setLastCheckTime()
        }
        return true
      }
    }
    if (manualCheck) {
      toast.success('已是最新版本')
    }
    return false
  }
  catch (error) {
    console.error('Failed to get version info:', error)
    if (manualCheck) {
      toast.error('检查更新失败')
    }
    return false
  }
  finally {
    if (manualCheck) {
      uni.hideLoading()
    }
  }
}

watch(showUpdatePopup, (isVisible) => {
  if (!isVisible && downloadTask.value) {
    downloadTask.value.abort()
    updateButtonText.value = '立即下载'
  }
})

onMounted(() => {
  if (props.autoCheck) {
    checkVersion()
  }
})

defineExpose({
  checkVersion,
})
</script>

<template>
  <wd-popup
    v-model="showUpdatePopup"
    :closable="!isForceUpdate"
    position="center"
    custom-style="border-radius: 12px; width: 80%; background-color: #f8f8f8;"
    @close="onPopupClose"
  >
    <view class="updater-popup">
      <view class="updater-popup__header">
        <text class="updater-popup__title">发现新版本 {{ latestVersionInfo?.versionName }}</text>
      </view>
      <scroll-view scroll-y class="updater-popup__content">
        <text class="updater-popup__description">{{ latestVersionInfo?.describe }}</text>
      </scroll-view>
      <view class="updater-popup__footer">
        <wd-button
          v-if="!isForceUpdate"
          type="info"
          custom-class="updater-popup__button"
          @click="onPopupClose"
        >稍后提醒</wd-button>
        <wd-button type="primary" custom-class="updater-popup__button" @click="handleUpdate">{{
          updateButtonText
        }}</wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<style scoped lang="scss">
.updater-popup {
  display: flex;
  flex-direction: column;
  padding: 20px;

  &__header {
    margin-bottom: 16px;
    text-align: center;
  }

  &__title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
  }

  &__content {
    max-height: 200px;
    margin-bottom: 24px;
    line-height: 1.6;
    font-size: 14px;
    color: #666;
  }

  &__description {
    white-space: pre-wrap;
  }

  &__footer {
    display: flex;
    justify-content: space-around;
    gap: 12px;
  }
}

:deep(.updater-popup__button) {
  width: 100%;
  border-radius: 8px !important;
}
</style>
