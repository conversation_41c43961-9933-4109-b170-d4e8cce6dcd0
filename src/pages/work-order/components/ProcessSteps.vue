<script setup lang="ts">
interface Process {
  processName?: string
  createdBy?: string
  createdAt?: string
  handleCheckItemsJson?: string
  id?: number
  processDesc?: string
  workOrderId?: number
  [key: string]: any
}

defineProps({
  processes: {
    type: Array as () => Process[],
    default: () => [],
  },
})

const parseCheckItems = (jsonString?: string) => {
  if (!jsonString)
    return []
  try {
    const items: Array<{
      checkItem: string
      resultType: 'text' | 'select' | 'image'
      resultContent: string
    }> = JSON.parse(jsonString)
    return items.map((item) => {
      let parsedResultContent: string | string[]
      if (item.resultType === 'image' && typeof item.resultContent === 'string') {
        parsedResultContent = item.resultContent
          .split(',')
          .map(s => s.trim())
          .filter(s => s)
      }
      else {
        parsedResultContent = item.resultContent
      }
      return {
        checkItem: item.checkItem,
        resultType: item.resultType,
        resultContent: parsedResultContent,
      }
    })
  }
  catch (e) {
    console.error('Failed to parse handleCheckItemsJson:', e)
    return []
  }
}

const previewImage = (imageUrls: string[], currentIndex: number) => {
  uni.previewImage({
    urls: imageUrls,
    current: currentIndex,
  })
}
</script>

<template>
  <view v-if="processes && processes.length > 0" class="p-2">
    <wd-steps :active="processes.length" vertical dot>
      <wd-step v-for="(process, pIndex) in processes" :key="pIndex">
        <template #title>
          <view class="process-header">
            {{ process.processName || '未命名流程' }} - {{ process.createdBy || '未知用户' }}
          </view>
          <view v-if="process.processDesc" class="process-desc bg-gray-50">
            {{ process.processDesc }}
          </view>
        </template>
        <template #description>
          <view class="mb-2 text-gray-500">{{ process.createdAt || '' }}</view>
          <view
            v-if="
              process.handleCheckItemsJson &&
                parseCheckItems(process.handleCheckItemsJson).length > 0
            "
            class="check-items-container rounded-md bg-gray-50 p-3 shadow-sm"
          >
            <view class="mb-1 text-gray-700 font-semibold">处理检查项：</view>
            <view
              v-for="(item, cIndex) in parseCheckItems(process.handleCheckItemsJson)"
              :key="cIndex"
              class="check-item mb-1.5"
            >
              <view class="text-gray-600">
                <text class="text-gray-800 font-medium">{{ item.checkItem }}: </text>
                <template v-if="item.resultType === 'text' || item.resultType === 'select'">
                  {{ item.resultContent || '-' }}
                </template>
                <template
                  v-else-if="
                    item.resultType === 'image' &&
                      Array.isArray(item.resultContent) &&
                      item.resultContent.length > 0
                  "
                >
                  <view class="image-list mt-1 flex flex-wrap gap-2">
                    <wd-img
                      v-for="(imgUrl, imgIdx) in (item.resultContent as string[])"
                      :key="imgIdx"
                      :src="imgUrl"
                      width="60px"
                      height="60px"
                      mode="aspectFill"
                      custom-class="rounded border border-gray-200"
                      @click="previewImage(item.resultContent as string[], imgIdx)"
                    />
                  </view>
                </template>
                <template v-else>-</template>
              </view>
            </view>
          </view>
        </template>
      </wd-step>
    </wd-steps>
  </view>
  <view v-else class="p-4 text-center text-gray-500">暂无工单流程信息。</view>
</template>

<style scoped>
.check-items-container {
  margin-top: 4px;
}
:deep(.wd-step__title) {
  .process-header {
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
  }
  .process-desc {
    padding: 10px;
    border-radius: 4px;
    color: #495057;
    margin-bottom: 12px;
    white-space: pre-wrap;
    font-size: 14px;
    line-height: 1.6;
  }
}
:deep(.wd-step__description) {
  font-size: 14px;
}
</style>
