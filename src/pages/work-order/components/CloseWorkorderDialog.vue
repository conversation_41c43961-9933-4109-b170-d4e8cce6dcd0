<script setup lang="ts">
import type { FormItemConfig } from '@/components/DynamicForm.vue'
import type { WorkOrderCloseReq } from '@/types/api/Workorder'
import { closeWorkOrder as apiCloseWorkOrder } from '@/api/workorder'
import DialogBox from '@/components/DialogBox.vue'
import DynamicForm from '@/components/DynamicForm.vue'
import { useDictStore } from '@/store/modules'
import { ref } from 'vue'
import { useToast } from 'wot-design-uni'

const props = defineProps<{
  orderCode: string
}>()

const emit = defineEmits(['success'])

const dictStore = useDictStore()
const toast = useToast()
const formRef = ref()
const dialogRef = ref()

// 关闭工单表单数据
const closeFormData = ref<WorkOrderCloseReq>({
  orderCode: '',
  closeReason: '',
  closeDesc: '',
})

// 表单配置
const formConfig: FormItemConfig[] = [
  {
    type: 'select' as const,
    field: 'closeReason',
    label: '关闭原因',
    required: true,
    options: dictStore.getDictByType('close_order_reason'),
    rules: [{ required: true, message: '请选择关闭原因', trigger: 'change' }],
  },
  {
    type: 'textarea' as const,
    field: 'closeDesc',
    label: '关闭描述',
    attrs: {
      maxlength: 500,
      placeholder: '请输入关闭工单的详细原因',
    },
  },
]

// 关单功能
const showCloseForm = async () => {
  // 设置当前工单编号
  closeFormData.value.orderCode = props.orderCode
  closeFormData.value.closeReason = ''
  closeFormData.value.closeDesc = ''

  // 打开对话框
  dialogRef.value.open()
}

// 确认关闭工单
const handleConfirm = async () => {
  try {
    // 使用表单校验
    const { valid } = await formRef.value.validate()
    if (valid) {
      toast.loading('正在关闭工单...')
      await apiCloseWorkOrder({
        orderCode: props.orderCode,
        closeReason: closeFormData.value.closeReason,
        closeDesc: closeFormData.value.closeDesc,
      })
      toast.success('工单关闭成功')
      emit('success')
      dialogRef.value.close()
    }
  }
  finally {
    toast.close()
  }
}

// 暴露方法给父组件
defineExpose({
  showCloseForm,
})
</script>

<template>
  <DialogBox
    ref="dialogRef"
    selector="close-workorder-dialog"
    title="关闭工单"
    cancel-text="取消"
    confirm-text="确认关闭"
    @confirm="handleConfirm"
  >
    <DynamicForm ref="formRef" v-model="closeFormData" :config="formConfig" label-position="top" />
  </DialogBox>
</template>
