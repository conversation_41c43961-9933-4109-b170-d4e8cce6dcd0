<script setup lang="ts">
import { applyWorkorderSparePartsFile } from '@/api/workorder'
import { ref } from 'vue'

interface SparePartItem {
  id: number
  partNo: string
  materialName?: string
  materialDesc: string
  partNo1: string
  poolId: string
  useStatus?: string
  noexchangeSettlePrice: string
  materialBrand: string
  materialType: string
  materialUnit?: string
  auditStatus: string
  auditResult?: string
  createDate: string
  sendDate?: string
  netGetDate?: string
  netOutDate?: string
  partSendTime?: string
  badReturnDate?: string
  receiveTime?: string
  oldReturnResult?: string
  applySource: string
  createBy: string
  serviceInfoId: string
  partFault?: string
  warehouseName: string
  transferCode?: string
  transport?: string
  requireStoreId: string
  fileList: any[]
  materialId: string
  transportState?: string
  transportName?: string
  actualBatchNum?: string
  orderSource?: string
}

interface SparePartResponse {
  fileList: any[]
  partPage: {
    content: SparePartItem[]
    totalElements: number
  }
}

const props = defineProps<{
  orderCode: string
}>()

const fileList = defineModel<any[]>('fileList', { default: () => [] })

const paging = ref()
const sparePartList = ref<SparePartItem[]>([])

const fetchSparePartData = async (pageNo: number, pageSize: number) => {
  try {
    const params = {
      serviceInfoId: props.orderCode,
      page: pageNo,
      row: pageSize,
    }

    const response: SparePartResponse = await applyWorkorderSparePartsFile(params)

    fileList.value = response.fileList || []

    paging.value?.completeByTotal(
      response.partPage.content,
      response.partPage.totalElements,
    )
  }
  catch (error) {
    console.error('Failed to fetch spare part list:', error)
    paging.value?.complete(false)
  }
}

// 获取登记状态文本
const getUseStatusText = (status?: string) => {
  const statusMap: Record<string, string> = {
    0: '未用',
    1: '已用',
    3: '性能故障',
  }
  return status ? statusMap[status] || '-' : '-'
}

// 判断是否可以登记
const canRegister = (item: SparePartItem) => {
  // 可以根据实际业务逻辑调整判断条件
  // 例如：已签收且未登记的备件可以登记
  // return item.receiveTime && !item.useStatus
  return true
}

// 处理登记操作
const handleRegister = (item: SparePartItem) => {
  uni.navigateTo({
    url: `/pages/work-order/spare-part/registration?id=${item.id}&orderCode=${props.orderCode}`,
  })
}

onMounted(() => {
  // 监听附件上传成功事件
  uni.$on('attachmentUploaded', (data: any) => {
    if (data.success && paging.value?.reload) {
      paging.value?.reload()
    }
  })

  // 监听备件申请成功事件
  uni.$on('updateSparePart', (data: any) => {
    if (data.success && paging.value?.reload) {
      paging.value?.reload()
    }
  })
})

// 页面卸载时移除事件监听
onUnmounted(() => {
  uni.$off('attachmentUploaded')
  uni.$off('updateSparePart')
})
</script>

<template>
  <z-paging
    ref="paging"
    v-model="sparePartList"
    class="spare-part-paging"
    :fixed="false"
    :default-page-size="20"
    :auto-hide-loading-after-first-loaded="false"
    :show-loading-more-when-reload="true"
    style="height: 240px"
    @query="fetchSparePartData"
  >
    <view class="spare-part-list">
      <view
        v-for="item in sparePartList"
        :key="item.id"
        class="spare-part-card"
      >
        <view class="card-content">
          <view class="card-row">
            <text class="label">备件单号</text>
            <text class="value">{{ item.id || '-' }}</text>
          </view>
          <view class="card-row">
            <text class="label">备件编码</text>
            <text class="value">{{ item.partNo || '-' }}</text>
          </view>
          <view class="card-row">
            <text class="label">备件名称</text>
            <text class="value">{{ item.materialDesc || '-' }}</text>
          </view>
          <view class="card-row">
            <text class="label">申请时间</text>
            <text class="value">{{ item.createDate || '-' }}</text>
          </view>
          <view class="card-row">
            <text class="label">签收时间</text>
            <text class="value">{{ item.receiveTime || '-' }}</text>
          </view>
          <view class="card-row">
            <text class="label">登记状态</text>
            <text class="value">{{ getUseStatusText(item.useStatus) }}</text>
          </view>
          <view class="card-row">
            <text class="label">登记时间</text>
            <text class="value">{{ '-' }}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="card-actions">
          <wd-button
            v-if="canRegister(item)"
            :round="false"
            type="primary"
            size="small"
            custom-class="register-btn"
            @click="handleRegister(item)"
          >
            登记
          </wd-button>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<style scoped lang="scss">
.card-header {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.accent-bar {
  width: 4px;
  height: 16px;
  background-color: #1890ff;
  margin-right: 8px;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.spare-part-paging {
  flex: 1;
}

.spare-part-card {
  background-color: #f2f3f5;
  border-radius: 8px;
  margin-bottom: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-content {
  padding: 16px;
}

.card-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.5;

  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  width: 88px;
  color: #91929e;
  flex-shrink: 0;
}

.value {
  flex: 1;
  color: #4b4b4b;
  word-break: break-all;
}

.card-actions {
  padding: 12px 16px 0;
  border-top: 1px solid #e8e9eb;
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
}

:deep(.register-btn) {
  min-width: 60px;
  height: 32px;
  font-size: 14px;
}
</style>
