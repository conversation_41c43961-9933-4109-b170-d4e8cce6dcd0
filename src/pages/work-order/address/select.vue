<script lang="ts" setup>
import { getAddressList } from '@/api/address'
import { ref } from 'vue'

interface Address {
  id: string
  province: string
  city: string
  region: string
  receiveAddress: string
  linkMan: string
  phone: string
  ifDefault: string // '1' 为默认，'0' 为非默认
  provinceCode?: string
  cityCode?: string
  regionCode?: string
  warehouseId?: string
}

const keyword = ref('')

const paging = ref<ZPagingRef>()
const addressList = ref<Address[]>([])
const pageSize = ref(20)
const addressTotal = ref(0)

const onSearch = () => {
  paging.value?.reload()
}

const queryList = async (pageNum: number, pageSize: number) => {
  try {
    const params = {
      page: pageNum,
      rows: pageSize,
    }

    // 如果有搜索关键词，可以添加搜索参数
    if (keyword.value) {
      // 根据实际API需要添加搜索参数
      // params.keyword = keyword.value
    }

    const response = await getAddressList(params)

    if (Object.keys(response).length > 0) {
      const { list, total } = response
      addressTotal.value = total
      paging.value?.completeByTotal(list, total)
    }
    else {
      console.error('获取地址列表失败:', response.error)
      paging.value?.complete([])
    }
  }
  catch (error) {
    console.error('获取地址列表失败:', error)
    paging.value?.complete(false)
  }
}

const { eventEmit } = useChannel()

const selectAddress = (address: Address) => {
  eventEmit('selectAddress', {
    province: address.province,
    city: address.city,
    region: address.region,
    receiveAddress: address.receiveAddress,
    linkMan: address.linkMan,
    phone: address.phone,
  })
  uni.navigateBack()
}

// 新增地址
const handleAddAddress = () => {
  uni.navigateTo({
    url: '/pages/work-order/address/add',
    events: {
      addressSaved: () => {
        // 地址保存成功后刷新列表
        paging.value?.reload()
      },
    },
  })
}

onShow(() => {
  if (paging.value) {
    paging.value.reload()
  }
})
</script>

<template>
  <view class="select-address-page">
    <view class="address-header">
      <text class="address-count">您已创建{{ addressTotal }}个地址，最多创建25个。</text>
    </view>
    <search-bar
      v-model="keyword"
      placeholder="请输入收件人/电话/地址"
      @search="onSearch"
    />
    <z-paging
      ref="paging"
      v-model="addressList"
      class="address-list-paging"
      :fixed="false"
      :default-page-size="pageSize"
      :auto-hide-loading-after-first-loaded="false"
      :show-loading-more-when-reload="true"
      use-virtual-list
      @query="queryList"
    >
      <view class="address-list-content">
        <view
          v-for="item in addressList"
          :key="item.id"
          class="address-card"
          @click="selectAddress(item)"
        >
          <view class="card-header">
            <view class="address-info">
              <text class="contact-name">{{ item.linkMan }}</text>
              <text class="contact-phone">{{ item.phone }}</text>
            </view>
            <wd-tag v-if="item.ifDefault === '1'" type="primary">默认</wd-tag>
          </view>
          <view class="card-body">
            <view class="address-detail">
              <text class="region">{{ item.province }}{{ item.city }}{{ item.region }}</text>
              <text class="detail">{{ item.receiveAddress }}</text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
    <wd-fab
      position="right-bottom"
      :z-index="5"
      :draggable="true"
      :expandable="false"
      @click="handleAddAddress"
    />
  </view>
</template>

<route lang="json">
{
  "layout": "pageBg",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "选择收货地址"
  }
}
</route>

<style scoped lang="scss">
.select-address-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .address-list-paging {
    flex: 1;

    .address-list-paging__empty-message {
      padding: 16px;
      color: #6b7280;
      text-align: center;
    }
  }

  .address-header {
    padding: 12px 16px;
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;

    .address-count {
      font-size: 14px;
      color: #666;
    }
  }

  .address-list-content {
    padding: 8px 12px;
  }

  .address-card {
    padding: 16px;
    margin-bottom: 12px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgb(0 0 0 / 5%);

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;

      .address-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .contact-name {
          font-size: 16px;
          font-weight: bold;
          color: #333;
        }

        .contact-phone {
          font-size: 14px;
          color: #666;
        }
      }

      .default-tag {
        padding: 2px 8px;
        background-color: #1890ff;
        color: #fff;
        font-size: 12px;
        border-radius: 4px;
      }
    }

    .card-body {
      .address-detail {
        .region {
          display: block;
          font-size: 14px;
          color: #666;
          margin-bottom: 4px;
        }

        .detail {
          display: block;
          font-size: 14px;
          color: #333;
          line-height: 1.5;
        }
      }
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

:deep(.uni-input-placeholder) {
  font-size: 12px;
  color: #9a9a9a;
}
</style>
