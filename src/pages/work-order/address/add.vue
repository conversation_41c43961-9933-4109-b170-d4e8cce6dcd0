<script setup lang="ts">
import type { FormItemConfig } from '@/components/DynamicForm.vue'
import { addAddress, getAddressById } from '@/api/address'
import DynamicForm from '@/components/DynamicForm.vue'
import Navbar from '@/components/Navbar.vue'
import { useRegion } from '@/composables/useRegion'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { useToast } from 'wot-design-uni'

const toast = useToast()

interface FormData {
  id?: string
  linkMan: string
  phone: string
  provinceCode: string
  province: string
  cityCode: string
  city: string
  regionCode: string
  region: string
  receiveAddress: string
}

const addressId = ref('')
const isEdit = ref(false)
const pageTitle = ref('新增地址')

const formData = ref<FormData>({
  linkMan: '',
  phone: '',
  provinceCode: '',
  province: '',
  cityCode: '',
  city: '',
  regionCode: '',
  region: '',
  receiveAddress: '',
})

const isSubmitting = ref(false)
const formRef = ref()

// const {
//   provinceOptions,
//   cityOptions,
//   regionOptions,
//   provinceMap,
//   cityMap,
//   regionMap,
//   setCity,
//   setRegion,
// } = useRegion()
const region = useRegion()

const { provinceOptions, cityOptions, regionOptions, provinceMap, cityMap, regionMap } = storeToRefs<any>(region)

// 动态表单配置
const formConfig: FormItemConfig[] = [
  {
    type: 'input',
    field: 'linkMan',
    label: '收货人',
    required: true,
    attrs: {
      placeholder: '请输入收货人姓名',
      clearable: true,
      maxlength: 15,
    },
  },
  {
    type: 'input',
    field: 'phone',
    label: '手机号码',
    required: true,
    attrs: {
      placeholder: '请输入手机号码',
      clearable: true,
      maxlength: 15,
      type: 'tel',
    },
  },
  {
    type: 'select',
    field: 'provinceCode',
    label: '所在省',
    required: true,
    options: provinceOptions,
    attrs: {
      placeholder: '请选择省份',
    },
  },
  {
    type: 'select',
    field: 'cityCode',
    label: '所在市',
    required: true,
    options: cityOptions,
    attrs: {
      placeholder: '请选择城市',
    },
  },
  {
    type: 'select',
    field: 'regionCode',
    label: '所在区',
    required: true,
    options: regionOptions,
    attrs: {
      placeholder: '请选择区县',
    },
  },
  {
    type: 'textarea',
    field: 'receiveAddress',
    label: '详细地址',
    required: true,
    attrs: {
      placeholder: '请输入详细地址',
      maxlength: 100,
      showWordLimit: true,
      autoHeight: true,
    },
  },
]

onLoad(async (options?: Record<string, string>) => {
  if (options?.id) {
    addressId.value = options.id
    isEdit.value = true
    pageTitle.value = '编辑地址'
    await loadAddressDetail()
  }
})

// 加载地址详情（编辑时）
const loadAddressDetail = async () => {
  try {
    const response = await getAddressById(addressId.value)
    if (response.success) {
      const addressDetail = response.result
      formData.value = { ...addressDetail }

      // 加载对应的市和区数据
      if (addressDetail.provinceCode) {
        region.setCity(addressDetail.provinceCode)
      }
      if (addressDetail.cityCode) {
        region.setRegion(addressDetail.cityCode)
      }
    }
  }
  catch (error) {
    console.error('加载地址详情失败:', error)
  }
}

// 监听表单变化
const handleFormChange = (field: string, val: any) => {
  if (field === 'provinceCode') {
    region.setCity(val.value)
    formData.value.cityCode = ''
    formData.value.city = ''
    formData.value.regionCode = ''
    formData.value.region = ''
    formData.value.province = provinceMap.value[val.value]
  }
  else if (field === 'cityCode') {
    region.setRegion(val.value)
    formData.value.regionCode = ''
    formData.value.region = ''
    formData.value.city = cityMap.value[val.value]
  }
  else if (field === 'regionCode') {
    formData.value.region = regionMap.value[val.value]
  }
}

// 提交保存
const handleSubmit = async () => {
  if (isSubmitting.value)
    return

  // 表单验证
  const isValid = await formRef.value?.validateWithHandleError()
  if (!isValid) {
    toast.warning('请完善必填信息')
    return
  }

  isSubmitting.value = true

  try {
    const params = { ...formData.value }

    await addAddress(params)
    toast.success(isEdit.value ? '地址修改成功' : '地址添加成功')

    // 返回上一页并通知刷新
    uni.navigateBack({
      success: () => {
        uni.$emit('addressSaved', { success: true })
      },
    })
  }
  catch (error) {
    console.error('保存地址失败:', error)
  }
  finally {
    isSubmitting.value = false
  }
}

// 取消操作
const handleCancel = () => {
  uni.navigateBack()
}
</script>

<template>
  <view class="add-address-page">
    <Navbar :title="pageTitle" :show-back="true" :safe-area-inset-top="true" :placeholder="true" />

    <view class="form-container">
      <view class="form-section">
        <!-- 使用DynamicForm组件 -->
        <DynamicForm
          ref="formRef"
          v-model="formData"
          :config="formConfig"
          @select-change="handleFormChange"
        />
      </view>
    </view>

    <view class="bottom-actions">
      <wd-button
        :round="false"
        type="info"
        plain
        custom-class="cancel-btn"
        @click="handleCancel"
      >
        取消
      </wd-button>
      <wd-button
        :round="false"
        type="primary"
        custom-class="submit-btn"
        :loading="isSubmitting"
        @click="handleSubmit"
      >
        保存
      </wd-button>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "新增地址"
  }
}
</route>

<style scoped lang="scss">
.add-address-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 80px;
}

.form-container {
  padding: 16px;
}

.form-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 12px;
  padding: 12px 16px;
  background-color: #fff;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.cancel-btn {
  flex: 1;
}

.submit-btn {
  flex: 2;
}
</style>
