<script setup lang="ts">
import type { Staff } from '@/types/api/User'
import type { WorkOrder, WorkOrderAppealInfo, WorkOrderHandleReq } from '@/types/api/Workorder'
import { getStaffList } from '@/api/user'
import {
  assignWorkOrder,
  auditPassWorkOrder,
  dispatchWorkOrder,
  getAppealsByOrderCode,
  getWorkOrderByOrderCode,
  handleWorkOrder,
} from '@/api/workorder'
import Navbar from '@/components/Navbar.vue'
import { useChannel } from '@/composables/useChannel'
import { usePageExtend } from '@/composables/usePageExtend'
import { useDictStore, useUserStore } from '@/store'
import { onLoad } from '@dcloudio/uni-app'
import { computed, ref } from 'vue'
import { useMessage, useToast } from 'wot-design-uni'
import AppealHistory from './components/AppealHistory.vue'
import AppealWorkorderAuditDialog from './components/AppealWorkorderAuditDialog.vue'
import AppealWorkorderDialog from './components/AppealWorkorderDialog.vue'
import AttachmentPreview from './components/AttachmentPreview.vue'
import CloseWorkorderDialog from './components/CloseWorkorderDialog.vue'
import FaultInfo from './components/FaultInfo.vue'
import ProcessSteps from './components/ProcessSteps.vue'
import RejectWorkorderDialog from './components/RejectWorkorderDialog.vue'
import SparePart from './components/SparePart.vue'
import StationInfo from './components/StationInfo.vue'
import WorkorderHandleForm from './components/WorkorderHandleForm.vue'
import WorkorderInfo from './components/WorkorderInfo.vue'

const dictStore = useDictStore()
const toast = useToast()
const userStore = useUserStore()
const userInfo = userStore.userInfo
const { options } = usePageExtend()
const workorder = ref<WorkOrder & { pdfUrl?: string, videoUrl?: string }>({})
const appealHistory = ref<WorkOrderAppealInfo[]>([])
const pageTitle = ref('')
const closeFormRef = ref()
const rejectFormRef = ref()
const appealFormRef = ref()
const appealAuditFormRef = ref()
const showBottomActions = ref(true)
const sparePartFileList = ref<any[]>([])
const currentAction = ref('')
const showAttachmentPreview = ref(false)

const isDispatching = ref(false)
const isHandling = ref(false)
const isPassing = ref(false)

const showAssignPicker = ref(false)
const staffList = ref<Staff[]>([])
const pickerColumns = computed(() => {
  return staffList.value.map(staff => ({ label: staff.staffName, value: staff.staffNo }))
})

// 添加操作菜单相关状态
const showActionSheet = ref(false)
const actionItems = computed(() => {
  const items = []
  if (workorder.value.solution?.file) {
    items.push({ name: '预览PDF文件', color: '#3B82F6' })
  }
  if (workorder.value.solution?.video) {
    items.push({ name: '查看视频', color: '#3B82F6' })
  }
  return items
})

// 点击预览按钮，打开操作菜单
const handlePreview = () => {
  showActionSheet.value = true
}

// 处理操作菜单选项点击
const handleActionClick = (evt: any) => {
  if (evt.item.name === '预览PDF文件') {
    const pdfUrl = workorder.value.solution?.file
    uni.downloadFile({
      url: pdfUrl,
      success(res) {
        const filePath = res.tempFilePath
        uni.openDocument({
          filePath,
          showMenu: true,
          fail() {
            uni.showToast({ title: 'PDF文件打开失败', icon: 'none' })
          },
        })
      },
      fail() {
        uni.showToast({ title: 'PDF文件下载失败', icon: 'none' })
      },
    })
  }
  else if (evt.item.name === '查看视频') {
    // 跳转到视频播放页面或直接播放视频
    const videoUrl = workorder.value.solution?.video
    uni.navigateTo({
      url: `/pages/common/video-player?url=${encodeURIComponent(videoUrl)}`,
    })
  }
}

const processTab = ref('handle')

const processTabs = computed(() => {
  const tags = [
    {
      value: 'handle',
      payload: {
        label: '工单处理',
      },
    },
    {
      value: 'flow',
      payload: {
        label: '工单流程',
      },
    },
    {
      value: 'sparePart',
      payload: {
        label: '备品备件',
      },
    },
  ]
  if (appealHistory.value && appealHistory.value.length > 0) {
    return [...tags, {
      value: 'appeal',
      payload: {
        label: '超期申诉',
      },
    }]
  }
  return tags
})

const showProcessTab = computed(() => {
  if (!workorder.value.orderStatus)
    return false

  if (
    ['TO_HEAD_DISPATCH', 'TO_SUB_CENTER_DISPATCH', 'TO_ASSIGN'].includes(
      workorder.value.orderStatus || '',
    )
  ) {
    return false
  }
  return true
})

const formData = ref<WorkOrderHandleReq>({})

const formRef = ref<any>(null)
const editable = computed(() => {
  return workorder.value.orderStatus === 'TO_PROCESS'
})

// 计算是否显示备件相关按钮
const showSparePartButtons = computed(() => {
  return processTab.value === 'sparePart' && userInfo?.userType === 'merchant'
})

// 计算是否显示常规底部按钮（非备件相关）
const showRegularBottomActions = computed(() => {
  return showBottomActions.value && processTab.value !== 'sparePart'
})

// 计算是否显示查看附件按钮（当fileList不为空时）
const showViewAttachmentButton = computed(() => {
  return sparePartFileList.value && sparePartFileList.value.length > 0
})

// 计算附件按钮文本
const attachmentButtonText = computed(() => {
  return showViewAttachmentButton.value ? '查看附件' : '上传附件'
})

const titleMap: Record<string, string> = {
  approve: '工单审核',
  handle: '工单处理',
  assign: '工单指派',
  view: '工单详情',
}

onLoad(async (options?: Record<string, string>) => {
  currentAction.value = options?.action || 'view'
  pageTitle.value = titleMap[options!.action] || '工单详情'
  showBottomActions.value = options!.action !== 'view'
  const orderCode = options?.orderCode

  dictStore.fetchDict([
    'fault_level',
    'work_order_type',
    'work_order_status',
    'dispatch_mode',
    'dispatch_review_permission',
    'review_mode',
    'work_order_source',
    'close_order_reason',
    'audit_reject_reason',
  ])
  if (orderCode) {
    try {
      const data = await getWorkOrderByOrderCode(orderCode)
      if (data) {
        workorder.value = data
        if (workorder.value.appealStatus) {
          getAppealsByOrderCode({ orderCode }).then((appeals) => {
            appealHistory.value = appeals
            // if (appeals && appeals.length > 0) {
            //   processTabs.push({
            //     value: 'appeal',
            //     payload: {
            //       label: '超期申诉',
            //     },
            //   })
            // }
          })
        }
        formData.value = {
          remark: data.remark || '',
          orderCode: data.orderCode,
        }

        if (Array.isArray(data.handleCheckItems) && data.handleCheckItems.length === 0) {
          formData.value.handleCheckItems
            = data.configCheckItems?.map(item => ({ ...item, resultContent: undefined })) || []
        }
        else {
          formData.value.handleCheckItems
            = data.handleCheckItems?.map((item) => {
              if (item.resultType === 'image') {
                return {
                  ...item,
                  resultContent: item
                    .resultContent!.split(',')
                    .map((ele: string) => ({ url: ele.trim() })),
                }
              }
              return item
            }) || []
        }
      }
      else {
        workorder.value = {} as WorkOrder
        formData.value = {
          remark: '',
          orderCode,
          handleCheckItems: [],
        }
        toast.error('未获取到工单数据')
      }
    }
    catch {
      toast.error('加载工单详情失败')
    }
  }
})

const faultPhotos = computed(() => {
  return workorder.value.faultInfo?.photos?.split(',') || []
})

// 关单功能
const doCloseWorkOrder = () => {
  closeFormRef.value?.showCloseForm()
}

// 驳回功能
const doRejectWorkOrder = () => {
  rejectFormRef.value?.showRejectForm()
}

const message = useMessage()
const { eventEmit } = useChannel()

// 辅助函数：获取最新工单数据、发送事件并返回
const emitUpdateAndNavigateBack = async (customMessage?: string) => {
  try {
    const updatedData = await getWorkOrderByOrderCode(workorder.value.orderCode!)
    if (updatedData) {
      eventEmit('workOrderUpdated', {
        orderCode: workorder.value.orderCode,
        updatedWorkOrder: updatedData,
      })
      toast.success(customMessage || '操作成功')
    }
    else {
      eventEmit('workOrderUpdated', { needsRefresh: true })
      toast.warning('操作成功，但获取最新状态失败，列表可能需要手动刷新')
    }
  }
  catch {
    eventEmit('workOrderUpdated', { needsRefresh: true })
    toast.error('获取最新工单信息失败')
  }
  finally {
    uni.navigateBack()
  }
}

// 下发功能
const doDispatchWorkOrder = async () => {
  if (isDispatching.value)
    return
  isDispatching.value = true
  try {
    await message.confirm({
      title: '下发工单',
      msg: '确认要下发此工单吗？下发后将进入下一处理环节',
      confirmButtonText: '确认下发',
      cancelButtonText: '取消',
    })

    await dispatchWorkOrder(workorder.value.orderCode!)
    await emitUpdateAndNavigateBack('下发成功')
  }
  finally {
    isDispatching.value = false
  }
}

const doAssignWorkOrder = async () => {
  if (staffList.value.length === 0) {
    try {
      const data = await getStaffList()
      staffList.value = data
    }
    catch {
      toast.error('获取人员列表失败')
      return
    }
  }
  showAssignPicker.value = true
}

const handleAssignConfirm = async (evt: any) => {
  const { value } = evt
  const selectedStaff = staffList.value.find(staff => staff.staffNo === value)
  if (!selectedStaff) {
    toast.error('未找到选择的人员')
    return
  }
  if (isHandling.value)
    return
  isHandling.value = true
  try {
    await message.confirm({
      title: '指派工单',
      msg: `确认要将此工单指派给 ${selectedStaff.staffName} 吗？`,
      confirmButtonText: '确认指派',
      cancelButtonText: '取消',
    })

    await assignWorkOrder({
      orderCode: workorder.value.orderCode!,
      staffNo: selectedStaff.staffNo,
    })
    await emitUpdateAndNavigateBack('指派成功')
  }
  finally {
    isHandling.value = false
  }
}

const doHandleWorkOrder = async () => {
  if (isHandling.value)
    return
  isHandling.value = true
  try {
    const { valid } = await formRef.value.validate()
    if (valid) {
      await message.confirm({
        title: '提交',
        msg: '确认提交工单处理结果吗',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
      })
      const params = {
        orderCode: formData.value.orderCode,
        remark: formData.value.remark,
        handleCheckItems:
          formData.value.handleCheckItems?.map((item) => {
            if (item.resultType === 'image' && Array.isArray(item.resultContent)) {
              return {
                ...item,
                resultContent: item.resultContent.map((file: any) => file.url || file).join(','),
              }
            }
            return item
          }) || [],
      }
      await handleWorkOrder(params)
      await emitUpdateAndNavigateBack('提交成功')
    }
  }
  finally {
    isHandling.value = false
  }
}

// 通过功能
const doPassWorkOrder = async () => {
  if (isPassing.value)
    return
  isPassing.value = true
  try {
    await message.confirm({
      title: '审核通过',
      msg: '确认要通过此工单审核吗？通过后工单将进入下一处理环节',
      confirmButtonText: '确认通过',
      cancelButtonText: '取消',
    })

    await auditPassWorkOrder(workorder.value.orderCode!)
    await emitUpdateAndNavigateBack('审核通过成功')
  }
  finally {
    isPassing.value = false
  }
}

// 处理关单成功的回调
const handleCloseSuccess = async () => {
  await emitUpdateAndNavigateBack('关单成功')
}

// 处理驳回成功的回调
const handleRejectSuccess = async () => {
  await emitUpdateAndNavigateBack('驳回成功')
}

// 打开申诉弹窗
const doAppealWorkOrder = () => {
  appealFormRef.value?.showAppealForm()
}

// 处理申诉成功的回调
const handleAppealSuccess = async () => {
  await emitUpdateAndNavigateBack('申诉提交成功')
}

// 打开申诉审核弹窗
const doAppealWorkOrderAudit = async () => {
  try {
    const appeals = await getAppealsByOrderCode({ orderCode: workorder.value.orderCode! })
    if (appeals && appeals.length > 0) {
      appealAuditFormRef.value?.show(appeals[0])
    }
    else {
      toast.error('未找到申诉记录')
    }
  }
  catch {
    toast.error('获取申诉记录失败')
  }
}

// 处理申诉审核成功的回调
const handleAppealAuditSuccess = async () => {
  await emitUpdateAndNavigateBack('申诉审核成功')
}

// 处理附件相关操作
const handleAttachment = () => {
  if (showViewAttachmentButton.value) {
    // 打开附件预览弹窗
    showAttachmentPreview.value = true
  }
  else {
    // 跳转到上传附件页面
    uni.navigateTo({
      url: `/pages/work-order/spare-part/upload-attachment?orderCode=${workorder.value.orderCode}`,
    })
  }
}

// 申请备件
const handleApplySparePart = () => {
  uni.navigateTo({
    url: `/pages/work-order/spare-part/apply?orderCode=${workorder.value.orderCode}`,
  })
}
</script>

<template>
  <view class="deal-page">
    <Navbar :title="pageTitle" :show-back="true" :safe-area-inset-top="true" :placeholder="true">
      <template #right>
        <view
          v-if="workorder.orderType && workorder.orderType !== 'report'"
          class="preview-button f-c-c px-2"
          @click="handlePreview"
        >
          <wd-icon name="view" size="20px" color="#333" />
        </view>
      </template>
    </Navbar>

    <StationInfo :station="workorder" />

    <WorkorderInfo :workorder="workorder" />

    <FaultInfo :fault-info="workorder.faultInfo" :photos="faultPhotos" />

    <template v-if="showProcessTab">
      <view class="segmented-container m-3">
        <wd-segmented v-model:value="processTab" :options="processTabs" custom-class="mb-4">
          <template #label="{ option }">
            <view class="segmented-item-content">
              <text>{{ option.payload.label }}</text>
            </view>
          </template>
        </wd-segmented>
      </view>

      <view class="card m-3 rounded-lg bg-white p-4 shadow-sm">
        <wd-tabs v-model="processTab" custom-class="hidden-tabs-nav">
          <wd-tab name="handle" :title="processTabs[0].payload.label">
            <WorkorderHandleForm
              ref="formRef"
              v-model:form-data="formData"
              :editable="editable"
              :station-code="workorder.stationCode!"
            />
          </wd-tab>
          <wd-tab name="flow" :title="processTabs[1].payload.label">
            <ProcessSteps :processes="workorder.processes" />
          </wd-tab>
          <wd-tab name="sparePart" :title="processTabs[2].payload.label">
            <SparePart
              v-model:file-list="sparePartFileList"
              :order-code="workorder.orderCode || ''"
            />
          </wd-tab>
          <wd-tab v-if="appealHistory.length" name="appeal" :title="processTabs[2].payload.label">
            <AppealHistory :appeals="appealHistory" />
          </wd-tab>
        </wd-tabs>
      </view>
    </template>

    <view v-if="showBottomActions" class="bottom-actions fixed-bottom">
      <view
        v-if="
          showRegularBottomActions &&
            ['TO_HEAD_DISPATCH'].includes(workorder.orderStatus || '') &&
            userInfo?.userType === 'haier' &&
            (userInfo?.subCenterUser === false || options?.from?.toUpperCase() === 'MY')
        "
        class="g-2 w-full"
      >
        <wd-button :round="false" type="info" plain :loading="isHandling" @click="doCloseWorkOrder">
          关单
        </wd-button>
        <wd-button
          :round="false"
          type="primary"
          :loading="isDispatching"
          @click="doDispatchWorkOrder"
        >
          下发
        </wd-button>
      </view>
      <view
        v-if="
          showRegularBottomActions &&
            ['TO_SUB_CENTER_DISPATCH'].includes(workorder.orderStatus || '') &&
            userInfo?.userType === 'haier' &&
            (userInfo?.subCenterUser === true || options?.from?.toUpperCase() === 'MY')
        "
        class="g-2 w-full"
      >
        <wd-button :round="false" type="info" plain :loading="isHandling" @click="doCloseWorkOrder">
          关单
        </wd-button>
        <wd-button
          :round="false"
          type="primary"
          :loading="isDispatching"
          @click="doDispatchWorkOrder"
        >下发</wd-button>
      </view>
      <view
        v-if="
          showRegularBottomActions &&
            ['TO_SUB_CENTER_DISPATCH', 'TO_HEAD_DISPATCH'].includes(workorder.orderStatus || '') &&
            userInfo?.userType === 'merchant' &&
            options?.from?.toUpperCase() === 'MY'
        "
        class="w-full flex"
      >
        <wd-button
          :round="false"
          type="primary"
          custom-class="w-full"
          :loading="isHandling"
          @click="doCloseWorkOrder"
        >
          关单
        </wd-button>
      </view>
      <wd-picker
        v-if="showRegularBottomActions && ['TO_ASSIGN'].includes(workorder.orderStatus || '')"
        v-model:visible="showAssignPicker"
        class="w-full flex"
        :columns="pickerColumns"
        title="选择指派人员"
        close-on-click-modal
        use-default-slot
        @confirm="handleAssignConfirm"
      >
        <wd-button
          :round="false"
          type="primary"
          custom-class="w-full"
          :loading="isHandling"
          @click="doAssignWorkOrder"
        >指派</wd-button>
      </wd-picker>
      <view v-if="showRegularBottomActions && ['TO_PROCESS'].includes(workorder.orderStatus || '')" class="w-full flex">
        <wd-button
          :round="false"
          type="primary"
          custom-class="w-full"
          :loading="isHandling"
          @click="doHandleWorkOrder"
        >提交</wd-button>
      </view>
      <view
        v-if="
          showRegularBottomActions &&
            ['TO_HEAD_AUDIT'].includes(workorder.orderStatus || '') &&
            userInfo?.userType === 'haier' &&
            userInfo?.subCenterUser === false
        "
        class="g-2 w-full"
      >
        <wd-button :round="false" type="info" plain @click="doRejectWorkOrder">驳回</wd-button>
        <wd-button :round="false" type="primary" :loading="isPassing" @click="doPassWorkOrder">通过</wd-button>
      </view>
      <view
        v-if="
          showRegularBottomActions &&
            ['TO_SUB_CENTER_AUDIT'].includes(workorder.orderStatus || '') &&
            userInfo?.userType === 'haier' &&
            userInfo?.subCenterUser === true
        "
        class="g-2 w-full"
      >
        <wd-button :round="false" type="info" plain @click="doRejectWorkOrder">驳回</wd-button>
        <wd-button :round="false" type="primary" :loading="isPassing" @click="doPassWorkOrder">通过</wd-button>
      </view>
      <view
        v-if="
          showRegularBottomActions &&
            ['FINISHED'].includes(workorder.orderStatus || '') &&
            // workorder.overTime === true &&
            userInfo?.userType === 'merchant' &&
            !workorder?.appealStatus
        "
        class="w-full"
      >
        <wd-button custom-class="w-full" :round="false" type="primary" :loading="isPassing" @click="doAppealWorkOrder">超期申诉</wd-button>
      </view>
      <view
        v-if="
          showRegularBottomActions &&
            ['FINISHED'].includes(workorder.orderStatus || '') &&
            // workorder.overTime === true &&
            userInfo?.userType === 'haier' &&
            workorder?.appealStatus === 'WAIT_AUDIT'
        "
        class="w-full"
      >
        <wd-button custom-class="w-full" :round="false" type="primary" :loading="isPassing" @click="doAppealWorkOrderAudit">审核</wd-button>
      </view>

      <!-- 备件相关按钮 -->
      <view v-if="showSparePartButtons && currentAction !== 'view'" class="g-2 w-full">
        <wd-button
          :round="false"
          type="info"
          plain
          @click="handleAttachment"
        >
          {{ attachmentButtonText }}
        </wd-button>
        <wd-button
          :round="false"
          type="primary"
          @click="handleApplySparePart"
        >
          申请备件
        </wd-button>
      </view>

      <!-- view模式下的备件按钮 -->
      <view v-if="showSparePartButtons && currentAction === 'view' && showViewAttachmentButton" class="w-full">
        <wd-button
          custom-class="w-full"
          :round="false"
          type="primary"
          @click="handleAttachment"
        >
          查看附件
        </wd-button>
      </view>
    </view>

    <!-- 操作菜单 -->
    <wd-action-sheet v-model="showActionSheet" :actions="actionItems" @select="handleActionClick" />

    <!-- 关闭工单表单组件 -->
    <CloseWorkorderDialog
      ref="closeFormRef"
      :order-code="workorder.orderCode || ''"
      @success="handleCloseSuccess"
    />

    <!-- 驳回工单表单组件 -->
    <RejectWorkorderDialog
      ref="rejectFormRef"
      :order-code="workorder.orderCode || ''"
      @success="handleRejectSuccess"
    />

    <!-- 申诉弹窗 -->
    <AppealWorkorderDialog
      ref="appealFormRef"
      :order-code="workorder.orderCode || ''"
      @success="handleAppealSuccess"
    />

    <!-- 申诉审核弹窗 -->
    <AppealWorkorderAuditDialog
      ref="appealAuditFormRef"
      @success="handleAppealAuditSuccess"
    />

    <!-- 附件预览弹窗 -->
    <AttachmentPreview
      v-model:visible="showAttachmentPreview"
      :file-list="sparePartFileList"
    />
    <!-- 指派人员选择器 -->
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "工单审核"
  }
}
</route>

<style scoped lang="scss">
.deal-page {
  padding-bottom: 80px;
  background-color: #f7f7f5;

  .segmented-container {
    --wot-segmented-item-bg-color: #fff;

    :deep(.wd-segmented__item.is-active) {
      color: white;
      background-color: $uni-color-primary;
    }

    :deep(.wd-segmented__item) {
      min-width: none;
      color: #91929e;
    }

    :deep(.wd-segmented) {
      width: 100%;
    }
  }

  .bottom-actions {
    z-index: 10;
    padding: 12px;
    background-color: #fff;
    box-shadow: 0 -2px 5px rgb(0 0 0 / 5%);

    :deep(.wd-picker__field) {
      display: flex;
      width: 100%;
    }
  }

  .hidden-tabs-nav {
    :deep(.wd-tabs__nav) {
      display: none !important;
    }
  }

  .preview-button {
    height: 100%;
    border-radius: 4px;
  }
}
</style>
