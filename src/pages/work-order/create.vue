<script setup lang="ts">
import type { FormItemConfig } from '@/components/DynamicForm.vue'
import type {
  WorkOrderConfig,
  WorkorderConfigPageParams,
  WorkOrderSubmitReq,
} from '@/types/api/Workorder'
import { getWorkOrderConfigList, submitWorkOrder } from '@/api/workorder'
import DynamicForm from '@/components/DynamicForm.vue'
import { onLoad } from '@dcloudio/uni-app'
import { computed, ref } from 'vue'
import { useToast } from 'wot-design-uni'

interface props extends WorkOrderSubmitReq {
  photos: any
}

const toast = useToast()

const formData = ref<props>({
  stationCode: '',
  orderSource: 'MANUAL_REPORT',
  problemDesc: '',
  photos: '',
  configId: undefined,
})

const formRef = ref<any>(null)

const faultCategoryOptions = ref<{ label: string, value: number | string | undefined }[]>([])

const fetchFaultCategories = async () => {
  const params: WorkorderConfigPageParams = {
    orderType: 'report',
  }
  const result = await getWorkOrderConfigList(params)

  faultCategoryOptions.value = result.map((item: WorkOrderConfig) => ({
    value: item.id,
    label: item.configName || '',
  }))
}

onLoad(() => {
  fetchFaultCategories()
})

const formConfig = computed<FormItemConfig[]>(() => [
  {
    type: 'input',
    field: 'stationCode',
    label: '电站编码',
    required: true,
    readonly: true,
    placeholder: '请先选择电站编码',
    attrs: {
      rightSlot: true,
    },
  },
  {
    type: 'select',
    field: 'configId',
    label: '故障现象',
    required: true,
    options: faultCategoryOptions.value,
    attrs: {
      type: 'radio',
    },
  },
  {
    type: 'upload',
    field: 'photos',
    label: '上传附件',
    required: true,
    attrs: {
      maxCount: 5,
      hint: '· 限制上传 5 个图片',
      sourceType: ['camera', 'album'],
    },
  },
  {
    type: 'textarea',
    field: 'problemDesc',
    label: '问题描述',
    attrs: {
      maxlength: 500,
      showWordLimit: true,
      customStyle: 'width: 100%',
    },
  },
])

function handleSelectStation() {
  uni.navigateTo({
    url: '/pages/station/select',
    events: {
      selectStation: (data: { stationCode: string }) => {
        if (data && data.stationCode) {
          formData.value.stationCode = data.stationCode
        }
      },
    },
  })
}

const { eventEmit } = useChannel()
// 辅助函数：获取最新工单数据、发送事件并返回
const emitUpdateAndNavigateBack = async (customMessage?: string) => {
  try {
    eventEmit('workOrderUpdated', {
      needsRefresh: true,
    })
    toast.success(customMessage || '操作成功')
  }
  finally {
    uni.navigateBack()
  }
}

const isSubmitting = ref(false)

async function handleSubmit() {
  if (isSubmitting.value)
    return
  if (!formRef.value)
    return

  const { valid } = await formRef.value.validateWithHandleError()

  if (valid) {
    isSubmitting.value = true
    const formValues = formData.value
    const payload: WorkOrderSubmitReq = {
      stationCode: formValues.stationCode,
      orderSource: formValues.orderSource,
      problemDesc: formValues.problemDesc,
      photos: formValues.photos.map((item: any) => item.url).join(','),
    }

    if (formValues.configId) {
      payload.configId = formValues.configId
    }

    try {
      await submitWorkOrder(payload)
      emitUpdateAndNavigateBack()
    }
    catch {
    }
    finally {
      isSubmitting.value = false
    }
  }
}
</script>

<template>
  <view class="page-container">
    <view class="form-card">
      <DynamicForm ref="formRef" v-model="formData" :config="formConfig">
        <template #right-stationCode>
          <wd-button
            custom-class="!min-w-16 !ml-2"
            type="primary"
            :round="false"
            @click="handleSelectStation"
          >
            选择
          </wd-button>
        </template>
      </DynamicForm>
    </view>
    <view class="submit-button-wrap">
      <wd-button type="primary" block :round="false" :loading="isSubmitting" @click="handleSubmit">
        提交工单
      </wd-button>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "提报工单"
  }
}
</route>

<style scoped lang="scss">
.page-container {
  box-sizing: border-box;
  min-height: 100vh;
  padding: 24rpx;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
  background-color: #f5f5f5;
}

.form-card {
  padding: 32rpx;
  margin-bottom: 24rpx;
  background-color: #fff;
  border-radius: 12rpx;
}

.submit-button-wrap {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  box-sizing: border-box;
  padding: 12px 16px calc(12px + env(safe-area-inset-bottom));
  background: #fff;
  border-top: 1px solid #f2f2f2;
}
</style>
