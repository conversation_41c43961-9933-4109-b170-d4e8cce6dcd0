<script setup lang="ts">
defineProps<{
  materialBrandOptions: { dictLabel: string, dictValue: string }[]
  materialTypeOptions: { dictLabel: string, dictValue: string }[]
}>()

const emit = defineEmits<{
  (e: 'apply', filters: { materialBrand?: string, materialType?: string }): void
}>()

const internalShow = defineModel<boolean>('modelValue')
const filters = reactive({
  materialBrand: '',
  materialType: '',
})

const handleReset = () => {
  filters.materialBrand = ''
  filters.materialType = ''
}

const handleClose = () => {
  internalShow.value = false
}

const handleApply = () => {
  emit('apply', { ...filters })
  internalShow.value = false
}
</script>

<template>
  <wd-popup v-model="internalShow" position="bottom" custom-style="height: 72vh">
    <view class="filter-popup">
      <view class="filter-popup__header">
        <text class="filter-popup__title">筛选</text>
        <view class="filter-popup__close" @click="handleClose">
          <text class="i-carbon-close filter-popup__close-icon"></text>
          <text>关闭</text>
        </view>
      </view>

      <view scroll-y class="filter-popup__content">
        <view class="filter-section">
          <text class="filter-section__title">备件品牌</text>
          <wd-radio-group v-model="filters.materialBrand" shape="dot" inline>
            <wd-radio
              v-for="item in materialBrandOptions"
              :key="item.dictValue"
              :value="item.dictValue"
              custom-class="filter-radio"
            >
              {{ item.dictLabel }}
            </wd-radio>
          </wd-radio-group>
        </view>

        <view class="filter-section">
          <text class="filter-section__title">备件分类</text>
          <wd-radio-group v-model="filters.materialType" shape="dot" inline>
            <wd-radio
              v-for="item in materialTypeOptions"
              :key="item.dictValue"
              :value="item.dictValue"
              custom-class="filter-radio"
            >
              {{ item.dictLabel }}
            </wd-radio>
          </wd-radio-group>
        </view>
      </view>

      <view class="filter-popup__footer">
        <view class="filter-popup__button-group">
          <wd-button
            type="info"
            :round="false"
            custom-class="reset-button"
            plain
            @click="handleReset"
          >
            重置
          </wd-button>
          <wd-button
            type="primary"
            :round="false"
            custom-class="submit-button"
            @click="handleApply"
          >
            提交
          </wd-button>
        </view>
      </view>
    </view>
  </wd-popup>
</template>

<style scoped lang="scss">
.filter-popup {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #f2f2f2;
  }

  &__title {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
  }

  &__close {
    display: flex;
    gap: 4px;
    align-items: center;
    font-size: 14px;
    color: #909399;
  }

  &__close-icon {
    font-size: 16px;
  }

  &__content {
    box-sizing: border-box;
    flex: 1;
    padding: 16px;
    overflow-y: auto;
  }

  &__footer {
    padding: 12px 16px;
    background-color: #fff;
    border-top: 1px solid #f2f2f2;
  }

  &__button-group {
    display: flex;
    gap: 12px;
  }
}

.filter-section {
  margin-bottom: 20px;

  &__title {
    display: block;
    margin-bottom: 12px;
    font-size: 16px;
    color: #303133;
  }
}

:deep(.filter-radio) {
  margin-right: 10px !important;
  margin-bottom: 8px;
}

:deep(.submit-button) {
  flex: 1;
  font-size: 16px;
}

:deep(.reset-button) {
  flex: 1;
  font-size: 16px;
}
</style>
