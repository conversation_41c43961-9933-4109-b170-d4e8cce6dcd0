<script lang="ts" setup>
import { getMaterial, getMerchantDict } from '@/api/common'
import { onLoad } from '@dcloudio/uni-app'
import { reactive, ref } from 'vue'
import MaterialFilterPopup from './components/MaterialFilterPopup.vue'

interface FilterParams {
  materialBrand?: string
  materialType?: string
}

interface SparePart {
  id: string
  materialCode: string
  materialName: string
  materialDesc: string
  materialBrand?: string
  materialType?: string
  materialUnit?: string
}

const keyword = ref('')
const showFilterPopup = ref(false)
const currentFilters = reactive({
  materialBrand: '',
  materialType: '',
})

const paging = ref<ZPagingRef>()
const sparePartList = ref<SparePart[]>([])
const pageSize = ref(20)
const materialBrandOptions = ref<any[]>([])
const materialTypeOptions = ref<any[]>([])
const materialBrandMap = ref<Record<string, string>>({})
const materialTypeMap = ref<Record<string, string>>({})

const onSearch = () => {
  paging.value?.reload()
}

const queryList = async (pageNum: number, pageSize: number) => {
  const params: any = {
    page: pageNum,
    row: pageSize,
  }

  if (keyword.value) {
    if (/^[A-Z0-9]*$/i.test(keyword.value)) {
      params.materialCode = keyword.value
    }
    else {
      params.materialDesc = keyword.value
    }
  }

  if (currentFilters.materialBrand) {
    params.materialBrand = currentFilters.materialBrand
  }
  if (currentFilters.materialType) {
    params.materialType = currentFilters.materialType
  }

  try {
    const res = await getMaterial(params)
    // 假设返回的数据结构包含content和totalElements
    const content = res.content || res || []
    const totalElements = res.totalElements || content.length

    paging.value?.completeByTotal(content, totalElements)
  }
  catch (error) {
    console.error('获取备件列表失败:', error)
    paging.value?.complete(false)
  }
}

onLoad(() => {
  getMerchantDict('materialBrand').then((res) => {
    materialBrandOptions.value = res
    materialBrandMap.value = res.reduce((acc: any, item) => {
      acc[item.dictValue!] = item.dictLabel!
      return acc
    }, {})
  })
  getMerchantDict('materialType').then((res) => {
    materialTypeOptions.value = res
    materialTypeMap.value = res.reduce((acc: any, item) => {
      acc[item.dictValue!] = item.dictLabel!
      return acc
    }, {})
  })
})

const { eventEmit } = useChannel()

const selectSparePart = (sparePart: SparePart) => {
  eventEmit('selectSparePart', {
    materialCode: sparePart.materialCode,
    materialName: sparePart.materialName,
    materialDesc: sparePart.materialDesc,
    materialId: sparePart.id,
  })
  uni.navigateBack()
}
</script>

<template>
  <view class="select-spare-part-page">
    <search-bar
      v-model="keyword"
      placeholder="请输入备件编码/备件名称"
      class="search-filter-container__search"
      :show-filter="true"
      @search="onSearch"
      @filter="showFilterPopup = true"
    />

    <z-paging
      ref="paging"
      v-model="sparePartList"
      class="spare-part-list-paging"
      :fixed="false"
      :default-page-size="pageSize"
      :auto-hide-loading-after-first-loaded="false"
      :show-loading-more-when-reload="true"
      use-virtual-list
      @query="queryList"
    >
      <view class="spare-part-list-content">
        <view
          v-for="item in sparePartList"
          :key="item.id || item.materialCode"
          class="spare-part-card"
          @click="selectSparePart(item)"
        >
          <view class="card-header">
            <text class="title">{{ item.materialCode }}</text>
          </view>
          <view class="card-body">
            <view v-if="item.materialBrand" class="detail-item">
              <text class="label">备件品牌</text>
              <text class="value">{{ materialBrandMap[item.materialBrand] }}</text>
            </view>
            <view v-if="item.materialType" class="detail-item">
              <text class="label">备件分类</text>
              <text class="value">{{ materialTypeMap[item.materialType] }}</text>
            </view>
            <view class="detail-item">
              <text class="label">备件名称</text>
              <text class="value">{{ item.materialDesc || '-' }}</text>
            </view>
            <view v-if="item.materialUnit" class="detail-item">
              <text class="label">备件规格</text>
              <text class="value">{{ item.materialUnit }}</text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>

    <MaterialFilterPopup
      v-model="showFilterPopup"
      :material-brand-options="materialBrandOptions"
      :material-type-options="materialTypeOptions"
      @apply="(filters: FilterParams) => {
        currentFilters.materialBrand = filters.materialBrand || ''
        currentFilters.materialType = filters.materialType || ''
        onSearch()
      }"
    />
  </view>
</template>

<route lang="json">
{
  "layout": "pageBg",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "选择备件"
  }
}
</route>

<style scoped lang="scss">
.select-spare-part-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .spare-part-list-paging {
    flex: 1;

    .spare-part-list-paging__empty-message {
      padding: 16px;
      color: #6b7280;
      text-align: center;
    }
  }

  .spare-part-list-content {
    padding: 8px 12px;
  }

  .spare-part-card {
    padding: 12px;
    margin-bottom: 10px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgb(0 0 0 / 5%);

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
      }
    }

    .card-body {
      .detail-item {
        display: flex;
        margin-bottom: 4px;
        font-size: 14px;
        line-height: 1.6;

        .label {
          flex-shrink: 0;
          width: 70px;
          margin-right: 8px;
          color: #666;
        }

        .value {
          flex: 1;
          color: #333;
          word-break: break-all;
        }
      }
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

:deep(.uni-input-placeholder) {
  font-size: 12px;
  color: #9a9a9a;
}
</style>
