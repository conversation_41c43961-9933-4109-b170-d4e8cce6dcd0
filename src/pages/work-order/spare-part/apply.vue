<script setup lang="ts">
import type { FormItemConfig } from '@/components/DynamicForm.vue'
import { getCdWarehouseAndAddressByMemberID } from '@/api/address'
import { applyWorkorderSpareParts } from '@/api/workorder'
import DynamicForm from '@/components/DynamicForm.vue'
import Navbar from '@/components/Navbar.vue'
import { useUserStore } from '@/store'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { useToast } from 'wot-design-uni'

const toast = useToast()

interface FormData {
  serviceInfoId: string
  materialCode: string
  materialId: number | null
  materialDesc: string
  applyNum: number
  province: string
  city: string
  region: string
  receiveAddress: string
  linkMan: string
  phone: string
}

const orderCode = ref('')
const formData = ref<FormData>({
  serviceInfoId: '',
  materialCode: '',
  materialId: null,
  materialDesc: '',
  applyNum: 1,
  province: '',
  city: '',
  region: '',
  receiveAddress: '',
  linkMan: '',
  phone: '',
})

const isSubmitting = ref(false)
const formRef = ref()
const userStore = useUserStore()

// 动态表单配置
const formConfig: FormItemConfig[] = [
  {
    type: 'input',
    field: 'serviceInfoId',
    label: '运维工单号',
    required: true,
    readonly: true,
    attrs: {
      placeholder: '运维工单号',
      readonly: true,
    },
  },
  {
    type: 'input',
    field: 'materialCode',
    label: '备件编码',
    required: true,
    readonly: true,
    placeholder: '请先选择备件编码',
    attrs: {
      rightSlot: true,
    },
  },
  {
    type: 'input',
    field: 'materialDesc',
    label: '备件名称',
    required: true,
    readonly: true,
    attrs: {
      placeholder: '请先选择备件编码',
      readonly: true,
    },
  },
  {
    type: 'input-number',
    field: 'applyNum',
    label: '申请数量',
    required: true,
    attrs: {
      min: 1,
      max: 999,
      step: 1,
    },
  },
  {
    type: 'custom',
    field: 'receiveAddress',
    label: '收货地址',
    required: true,
    customSlot: 'custom-receiveAddress',
    attrs: {
      placeholder: '请选择收货地址',
      rightSlot: true,
    },
  },
]

onLoad(async (options?: Record<string, string>) => {
  if (options?.orderCode) {
    orderCode.value = options.orderCode
    formData.value.serviceInfoId = options.orderCode
  }

  // 获取默认收货地址
  await loadDefaultAddress()
})

// 加载默认收货地址
const loadDefaultAddress = async () => {
  try {
    const memberId = userStore.userInfo!.opMemberId
    const response = await getCdWarehouseAndAddressByMemberID(memberId!)
    if (response.warehouseAddress) {
      const address = response.warehouseAddress
      formData.value.province = address.province
      formData.value.city = address.city
      formData.value.region = address.region
      formData.value.receiveAddress = address.receiveAddress
      formData.value.linkMan = address.linkMan
      formData.value.phone = address.phone
    }
  }
  catch (error) {
    console.error('获取默认地址失败:', error)
  }
}

// 选择备件
const handleSelectSparePart = () => {
  uni.navigateTo({
    url: '/pages/work-order/spare-part/select',
    events: {
      selectSparePart: (data: { materialId: number, materialCode: string, materialDesc: string }) => {
        if (data && data.materialId) {
          formData.value.materialId = data.materialId
          formData.value.materialCode = data.materialCode
          formData.value.materialDesc = data.materialDesc
        }
      },
    },
  })
}

// 选择收货地址
const handleSelectAddress = () => {
  uni.navigateTo({
    url: '/pages/work-order/address/select',
    events: {
      selectAddress: (data: { province: string, city: string, region: string, receiveAddress: string, linkMan: string, phone: string }) => {
        if (data) {
          formData.value.province = data.province
          formData.value.city = data.city
          formData.value.region = data.region
          formData.value.receiveAddress = data.receiveAddress
          formData.value.linkMan = data.linkMan
          formData.value.phone = data.phone
        }
      },
    },
  })
}

// 提交申请
const handleSubmit = async () => {
  if (isSubmitting.value)
    return

  // 表单验证
  const isValid = await formRef.value?.validateWithHandleError()
  if (!isValid) {
    toast.warning('请完善必填信息')
    return
  }

  isSubmitting.value = true

  try {
    const params = {
      serviceInfoId: formData.value.serviceInfoId,
      materialId: formData.value.materialId,
      applyNum: formData.value.applyNum,
      province: formData.value.province,
      city: formData.value.city,
      region: formData.value.region,
      receiveAddress: formData.value.receiveAddress,
      linkMan: formData.value.linkMan,
      phone: formData.value.phone,
      memberId: userStore.userInfo?.memberId,
    }

    await applyWorkorderSpareParts(params)
    toast.success('备件申请提交成功')

    // 返回上一页并传递成功标识
    uni.navigateBack({
      success: () => {
        // 可以通过事件通知上一页刷新
        uni.$emit('updateSparePart', { success: true })
      },
    })
  }
  catch (error) {
    console.error('申请备件失败:', error)
    // toast.error('申请备件失败，请重试')
  }
  finally {
    isSubmitting.value = false
  }
}

// 取消操作
const handleCancel = () => {
  uni.navigateBack()
}
</script>

<template>
  <view class="apply-spare-part-page">
    <Navbar title="申请备件" :show-back="true" :safe-area-inset-top="true" :placeholder="true" />

    <view class="page-header">
      <view class="header-content">
        <view class="subtitle">工单编号：{{ orderCode }}</view>
      </view>
    </view>

    <view class="form-container">
      <view class="form-section">
        <view class="section-title">备件申请信息</view>

        <!-- 使用DynamicForm组件 -->
        <DynamicForm
          ref="formRef"
          v-model="formData"
          :config="formConfig"
        >
          <template #right-materialCode>
            <wd-button
              custom-class="!min-w-16 !ml-2"
              type="primary"
              :round="false"
              @click="handleSelectSparePart"
            >
              选择
            </wd-button>
          </template>

          <template #custom-receiveAddress>
            <view class="address-display" @click="handleSelectAddress">
              <view v-if="formData.province || formData.city || formData.region || formData.receiveAddress" class="address-content">
                <view class="address-line">{{ formData.province }}{{ formData.city }}{{ formData.region }}</view>
                <view class="address-line">{{ formData.receiveAddress }}</view>
                <view class="address-line">{{ formData.linkMan }} {{ formData.phone }}</view>
              </view>
              <view v-else class="address-placeholder">
                请选择收货地址
              </view>
              <view class="address-action">
                <wd-button
                  custom-class="!min-w-16"
                  type="primary"
                  :round="false"
                  size="small"
                >
                  {{ formData.receiveAddress ? '修改地址' : '选择地址' }}
                </wd-button>
              </view>
            </view>
          </template>
        </DynamicForm>
      </view>
    </view>

    <view class="bottom-actions">
      <wd-button
        :round="false"
        type="info"
        plain
        custom-class="cancel-btn"
        @click="handleCancel"
      >
        取消
      </wd-button>
      <wd-button
        :round="false"
        type="primary"
        custom-class="submit-btn"
        :loading="isSubmitting"
        @click="handleSubmit"
      >
        提交申请
      </wd-button>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "申请备件"
  }
}
</route>

<style scoped lang="scss">
.apply-spare-part-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 80px;
}

.page-header {
  background-color: #fff;
  padding: 20px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-content {
  .subtitle {
    font-size: 14px;
    color: #666;
  }
}

.form-container {
  padding: 16px;
}

.form-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 12px;
  padding: 12px 16px;
  background-color: #fff;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.cancel-btn {
  flex: 1;
}

.submit-btn {
  flex: 2;
}

.address-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  cursor: pointer;

  &:active {
    background-color: #e9ecef;
  }
}

.address-content {
  flex: 1;
  margin-right: 12px;
  text-align: left;
}

.address-line {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 2px;

  &:last-child {
    margin-bottom: 0;
  }
}

.address-placeholder {
  flex: 1;
  font-size: 14px;
  color: #999;
  margin-right: 12px;
  text-align: left;
}

.address-action {
  flex-shrink: 0;
  display: flex;
}
</style>
