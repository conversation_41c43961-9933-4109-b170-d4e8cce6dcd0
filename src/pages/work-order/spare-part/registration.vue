<script setup lang="ts">
import type { FormItemConfig } from '@/components/DynamicForm.vue'
import { checkWorkorderSparePartsRegistration, submitWorkorderSparePartsRegistration } from '@/api/workorder'
import DynamicForm from '@/components/DynamicForm.vue'
import Navbar from '@/components/Navbar.vue'
import { useUserStore } from '@/store'
import { onLoad } from '@dcloudio/uni-app'
import { computed, ref } from 'vue'
import { useToast } from 'wot-design-uni'

const toast = useToast()
const userStore = useUserStore()

interface RegistrationFormData {
  id: string
  serviceInfoId: string
  partNo: string
  materialDesc: string
  createDate: string
  poolId: string
  warehouseName: string
  requireStoreId: string
  useStatus: string
  partFault: string
  oldpartReturn: boolean
  oldReturnResult: string
  fileList: any[]
}

const formRef = ref()
const isSubmitting = ref(false)
const isLoading = ref(false)

// 页面参数
const sparePartId = ref('')
const orderCode = ref('')

// 表单数据
const formData = ref<RegistrationFormData>({
  id: '',
  serviceInfoId: '',
  partNo: '',
  materialDesc: '',
  createDate: '',
  poolId: '',
  warehouseName: '',
  requireStoreId: '',
  useStatus: '',
  partFault: '',
  oldpartReturn: false,
  oldReturnResult: '',
  fileList: [],
})

// 登记状态选项
const useStatusOptions = [
  { label: '未用', value: '0' },
  { label: '已用', value: '1' },
  { label: '性能故障', value: '3' },
]

// 动态表单配置
const formConfig = ref<FormItemConfig[]>(() => [
  {
    type: 'input',
    field: 'serviceInfoId',
    label: '运维工单号',
    required: false,
    attrs: {
      disabled: true,
      placeholder: '运维工单号',
    },
  },
  {
    type: 'input',
    field: 'partNo',
    label: '备件编码',
    required: false,
    attrs: {
      disabled: true,
      placeholder: '备件编码',
    },
  },
  {
    type: 'input',
    field: 'materialDesc',
    label: '备件名称',
    required: false,
    attrs: {
      disabled: true,
      placeholder: '备件名称',
    },
  },
  {
    type: 'input',
    field: 'createDate',
    label: '申请时间',
    required: false,
    attrs: {
      disabled: true,
      placeholder: '申请时间',
    },
  },
  {
    type: 'input',
    field: 'poolId',
    label: '检出仓库',
    required: false,
    attrs: {
      disabled: true,
      placeholder: '检出仓库',
    },
  },
  {
    type: 'input',
    field: 'warehouseName',
    label: '出库仓库',
    required: false,
    attrs: {
      disabled: true,
      placeholder: '出库仓库',
    },
  },
  {
    type: 'select',
    field: 'useStatus',
    label: '登记状态',
    required: true,
    attrs: {
      placeholder: '请选择登记状态',
      options: useStatusOptions,
    },
  },
  {
    type: 'textarea',
    field: 'partFault',
    label: '性能故障描述',
    required: false,
    attrs: {
      placeholder: '请填写性能故障描述',
      maxlength: 200,
      showWordLimit: true,
    },
    customProps: {
      disabled: computed(() => formData.value.useStatus !== '3'),
    },
  },
  {
    type: 'switch',
    field: 'oldpartReturn',
    label: '无旧件返回',
    required: false,
  },
  {
    type: 'textarea',
    field: 'oldReturnResult',
    label: '无旧件返还描述',
    required: false,
    attrs: {
      placeholder: '请填写无旧件返还描述',
      maxlength: 200,
      showWordLimit: true,
    },
    customProps: {
      disabled: computed(() => !formData.value.oldpartReturn),
    },
  },
  {
    type: 'upload',
    field: 'fileList',
    label: '附件',
    required: false,
    attrs: {
      maxCount: 1,
      hint: '· 限制上传 1 个图片',
      sourceType: ['camera', 'album'],
    },
  },
])

// 页面加载
onLoad((options?: Record<string, string>) => {
  if (options?.id) {
    sparePartId.value = options.id
  }
  if (options?.orderCode) {
    orderCode.value = options.orderCode
  }

  if (sparePartId.value) {
    loadRegistrationData()
  }
})

// 加载登记数据
const loadRegistrationData = async () => {
  if (!sparePartId.value)
    return

  isLoading.value = true
  try {
    const response = await checkWorkorderSparePartsRegistration({
      id: sparePartId.value,
      serviceInfoId: orderCode.value,
    })

    if (response && response.success) {
      const data = response.result
      formData.value = {
        id: data.id || sparePartId.value,
        serviceInfoId: data.serviceInfoId || '',
        partNo: data.partNo || '',
        materialDesc: data.materialDesc || '',
        createDate: data.createDate || '',
        poolId: data.poolId || '',
        warehouseName: data.warehouseName || '',
        requireStoreId: data.requireStoreId || '',
        useStatus: data.useStatus || '',
        partFault: data.partFault || '',
        oldpartReturn: data.oldpartReturn === '1',
        oldReturnResult: data.oldReturnResult || '',
        fileList: data.fileList || [],
      }
    }
  }
  catch (error) {
    console.error('加载登记数据失败:', error)
    toast.error('加载数据失败，请重试')
  }
  finally {
    isLoading.value = false
  }
}

// 表单验证
const validateForm = (): boolean => {
  // 检查必填字段
  if (!formData.value.useStatus) {
    toast.error('请选择登记状态')
    return false
  }

  // 性能故障时必须填写故障描述
  if (formData.value.useStatus === '3' && !formData.value.partFault) {
    toast.error('请填写性能故障描述')
    return false
  }

  // 无旧件返回时必须填写描述和上传附件
  if (formData.value.oldpartReturn) {
    if (!formData.value.oldReturnResult) {
      toast.error('请填写无旧件返还描述')
      return false
    }
    if (!formData.value.fileList || formData.value.fileList.length === 0) {
      toast.error('勾选无旧件返还时必须上传附件')
      return false
    }
  }

  return true
}

// 提交登记
const handleSubmit = async () => {
  if (!validateForm())
    return

  isSubmitting.value = true

  try {
    // 处理文件列表
    const fileArr = formData.value.fileList?.map(file => ({
      fileUrl: file.url || file.response?.result || '',
    })) || []

    const params = {
      id: formData.value.id,
      serviceInfoId: formData.value.serviceInfoId,
      requireStoreId: formData.value.requireStoreId,
      oldpartReturn: formData.value.oldpartReturn ? '1' : '0',
      oldReturnResult: formData.value.oldpartReturn ? formData.value.oldReturnResult : '',
      applyBy: userStore.userInfo?.memberId,
      useStatus: formData.value.useStatus,
      partFault: formData.value.useStatus === '3' ? formData.value.partFault : '',
      fileList: fileArr,
    }

    const response = await submitWorkorderSparePartsRegistration(params)

    if (response && response.success) {
      toast.success('登记成功')

      // 返回上一页并通知刷新
      uni.navigateBack({
        success: () => {
          uni.$emit('updateSparePart', { success: true })
        },
      })
    }
    else {
      toast.error(response?.error || response?.message || '登记失败')
    }
  }
  catch (error) {
    console.error('备件登记失败:', error)
    toast.error('登记失败，请重试')
  }
  finally {
    isSubmitting.value = false
  }
}

// 取消操作
const handleCancel = () => {
  uni.navigateBack()
}
</script>

<template>
  <view class="registration-page">
    <Navbar title="备件登记" :show-back="true" :safe-area-inset-top="true" :placeholder="true" />

    <view class="page-header">
      <view class="header-content">
        <view class="subtitle">工单编号：{{ orderCode }}</view>
      </view>
    </view>

    <view v-if="isLoading" class="loading-container">
      <wd-loading type="spinner" />
      <text class="loading-text">加载中...</text>
    </view>

    <view v-else class="form-container">
      <view class="form-section">
        <view class="section-title">备件登记信息</view>

        <DynamicForm
          ref="formRef"
          v-model="formData"
          :config="formConfig"
        />
      </view>

      <view class="form-actions">
        <wd-button
          :round="false"
          type="info"
          plain
          custom-class="action-btn cancel-btn"
          @click="handleCancel"
        >
          取消
        </wd-button>
        <wd-button
          :round="false"
          type="primary"
          :loading="isSubmitting"
          custom-class="action-btn submit-btn"
          @click="handleSubmit"
        >
          保存
        </wd-button>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.registration-page {
  min-height: 100vh;
  background-color: #f5f6fa;
}

.page-header {
  background-color: #fff;
  padding: 16px;
  margin-bottom: 12px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.subtitle {
  font-size: 14px;
  color: #666;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loading-text {
  margin-top: 12px;
  font-size: 14px;
  color: #666;
}

.form-container {
  flex: 1;
  padding-bottom: 100px;
}

.form-section {
  background-color: #fff;
  margin-bottom: 12px;
  padding: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.form-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 12px;
  padding: 16px;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  z-index: 100;
}

.action-btn {
  flex: 1;
  height: 44px;
}

.cancel-btn {
  border-color: #d9d9d9;
  color: #666;
}

.submit-btn {
  background-color: #1890ff;
  border-color: #1890ff;
}
</style>
