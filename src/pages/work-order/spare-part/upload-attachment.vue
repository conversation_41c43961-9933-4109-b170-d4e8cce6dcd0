<script setup lang="ts">
import type { FormItemConfig } from '@/components/DynamicForm.vue'
import { saveWorkorderSparePartsFile } from '@/api/workorder'
import DynamicForm from '@/components/DynamicForm.vue'
import Navbar from '@/components/Navbar.vue'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { useToast } from 'wot-design-uni'

const toast = useToast()

interface FormData {
  damagedPart: any[]
  parallelMeshBox: any[]
  Nameplate: any[]
}

const orderCode = ref('')
const formData = ref<FormData>({
  damagedPart: [],
  parallelMeshBox: [],
  Nameplate: [],
})

const isSubmitting = ref(false)
const formRef = ref()

// 动态表单配置
const formConfig: FormItemConfig[] = [
  {
    type: 'upload',
    field: 'damagedPart',
    label: '损坏部件',
    required: false,
    attrs: {
      maxCount: 1,
      hint: '· 限制上传 1 个图片',
      sourceType: ['camera', 'album'],
    },
  },
  {
    type: 'upload',
    field: 'parallelMeshBox',
    label: '并网箱',
    required: false,
    attrs: {
      maxCount: 1,
      hint: '· 限制上传 1 个图片',
      sourceType: ['camera', 'album'],
    },
  },
  {
    type: 'upload',
    field: 'Nameplate',
    label: '铭牌',
    required: false,
    attrs: {
      maxCount: 1,
      hint: '· 限制上传 1 个图片',
      sourceType: ['camera', 'album'],
    },
  },
]

onLoad((options?: Record<string, string>) => {
  if (options?.orderCode) {
    orderCode.value = options.orderCode
  }
})

// 提交附件
const handleSubmit = async () => {
  if (isSubmitting.value)
    return

  // 检查是否至少上传了一个附件
  const hasAnyFile = (formData.value.damagedPart && formData.value.damagedPart.length > 0)
    || (formData.value.parallelMeshBox && formData.value.parallelMeshBox.length > 0)
    || (formData.value.Nameplate && formData.value.Nameplate.length > 0)

  if (!hasAnyFile) {
    toast.warning('请至少上传一个附件')
    return
  }

  isSubmitting.value = true

  try {
    const fileList = []

    // 处理损坏部件附件
    if (formData.value.damagedPart && formData.value.damagedPart.length > 0) {
      fileList.push({
        detailId: 'damagedPart',
        fileUrl: formData.value.damagedPart[0].url || formData.value.damagedPart[0],
      })
    }

    // 处理并网箱附件
    if (formData.value.parallelMeshBox && formData.value.parallelMeshBox.length > 0) {
      fileList.push({
        detailId: 'parallelMeshBox',
        fileUrl: formData.value.parallelMeshBox[0].url || formData.value.parallelMeshBox[0],
      })
    }

    // 处理铭牌附件
    if (formData.value.Nameplate && formData.value.Nameplate.length > 0) {
      fileList.push({
        detailId: 'Nameplate',
        fileUrl: formData.value.Nameplate[0].url || formData.value.Nameplate[0],
      })
    }

    const params = {
      serviceInfoId: orderCode.value,
      fileList,
    }

    await saveWorkorderSparePartsFile(params)
    toast.success('附件上传成功')

    // 返回上一页并传递成功标识
    uni.navigateBack({
      success: () => {
        // 可以通过事件通知上一页刷新
        uni.$emit('attachmentUploaded', { success: true })
      },
    })
  }
  catch (error) {
    console.error('上传附件失败:', error)
    toast.error('上传附件失败，请重试')
  }
  finally {
    isSubmitting.value = false
  }
}

// 取消操作
const handleCancel = () => {
  uni.navigateBack()
}
</script>

<template>
  <view class="upload-attachment-page">
    <Navbar title="上传附件" :show-back="true" :safe-area-inset-top="true" :placeholder="true" />

    <view class="page-header">
      <view class="header-content">
        <view class="subtitle">工单编号：{{ orderCode }}</view>
      </view>
    </view>

    <view class="form-container">
      <view class="form-section">
        <view class="section-title">请上传相关附件</view>

        <!-- 使用DynamicForm组件 -->
        <DynamicForm
          ref="formRef"
          v-model="formData"
          :config="formConfig"
        />
      </view>
    </view>

    <view class="bottom-actions">
      <wd-button
        :round="false"
        type="info"
        plain
        custom-class="cancel-btn"
        @click="handleCancel"
      >
        取消
      </wd-button>
      <wd-button
        :round="false"
        type="primary"
        custom-class="submit-btn"
        :loading="isSubmitting"
        @click="handleSubmit"
      >
        提交
      </wd-button>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "上传附件"
  }
}
</route>

<style scoped lang="scss">
.upload-attachment-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 80px;
}

.page-header {
  background-color: #fff;
  padding: 20px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-content {
  .title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
  }

  .subtitle {
    font-size: 14px;
    color: #666;
  }
}

.form-container {
  padding: 16px;
}

.form-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.upload-item {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.upload-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.upload-component {
  width: 100%;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 12px;
  padding: 12px 16px;
  background-color: #fff;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.cancel-btn {
  flex: 1;
}

.submit-btn {
  flex: 2;
}
</style>
