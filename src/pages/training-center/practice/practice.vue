<script setup lang="ts">
import type { PracticeResult } from '@/types/api/Exam'
import { completePractice, getPracticeDetail, updatePracticeProgress } from '@/api/exam'
import { onLoad } from '@dcloudio/uni-app'
import { redirectTo } from '@uni-helper/uni-promises'
import { computed, ref, watch } from 'vue'
import { useMessage, useToast } from 'wot-design-uni'

const toast = useToast()
const message = useMessage()

const practiceDetail = ref<PracticeResult | null>(null)
const currentQuestionIndex = ref(0)
const userAnswers = ref<Record<string, any>>({})
const loading = ref(false)
const practiceId = ref<number>()
const submitting = ref(false)

// 页面加载时获取参数
onLoad((options: any) => {
  if (options.practiceId) {
    practiceId.value = Number(options.practiceId)
    fetchPracticeDetail()
  }
})

// 获取练习详情
const fetchPracticeDetail = async () => {
  if (!practiceId.value) {
    console.error('练习ID不存在')
    toast.error('练习ID不存在')
    return
  }

  try {
    loading.value = true
    const result = await getPracticeDetail({ practiceId: practiceId.value })
    practiceDetail.value = result

    // 恢复已答题目
    result.questions.forEach((question: any) => {
      const questionId = question.id.toString()
      const userAnswer = question.userAnswer

      if (userAnswer) {
        if (question.type === 'MULTIPLE') {
          // 多选题答案用逗号分隔
          userAnswers.value[questionId] = userAnswer.split(',')
        }
        else {
          // 单选题直接存储答案
          userAnswers.value[questionId] = userAnswer
        }
        // 标记为已确认
        confirmedQuestions.value.add(questionId)
      }
    })

    // 如果有未完成的题目，定位到第一个未答题目
    if (practiceDetail.value?.questions) {
      const firstUnansweredIndex = practiceDetail.value.questions.findIndex(
        (question: any) => !userAnswers.value[question.id.toString()],
      )
      if (firstUnansweredIndex > -1) {
        currentQuestionIndex.value = firstUnansweredIndex
      }

      // 检查并设置初始题目的解析显示状态
      const initialQuestion = practiceDetail.value.questions[currentQuestionIndex.value]
      if (initialQuestion) {
        const questionId = initialQuestion.id.toString()
        showAnalysis.value = confirmedQuestions.value.has(questionId)
      }
    }
  }
  catch (error) {
    console.error('获取练习详情失败:', error)
    toast.error('获取练习详情失败')
  }
  finally {
    loading.value = false
  }
}

// 当前题目
const currentQuestion = computed(() => {
  if (!practiceDetail.value?.questions)
    return null
  return practiceDetail.value.questions[currentQuestionIndex.value]
})

// 答题统计
const answeredCount = computed(() => {
  if (!practiceDetail.value?.questions)
    return 0
  return practiceDetail.value.questions.filter((_, index) => isQuestionAnswered(index)).length
})

// 获取题目类型文本
const getQuestionTypeText = (type: string) => {
  switch (type) {
    case 'SINGLE':
      return '单选题'
    case 'MULTIPLE':
      return '多选题'
    default:
      return type
  }
}

// 是否显示答案解析
const showAnalysis = ref(false)
// 记录已确认答案的题目ID
const confirmedQuestions = ref<Set<string>>(new Set())

// 当前题目是否已确认答案
const isCurrentQuestionConfirmed = computed(() => {
  if (!currentQuestion.value)
    return false
  const questionId = currentQuestion.value.id.toString()
  return confirmedQuestions.value.has(questionId)
})

// 选择答案
const selectAnswer = (optionKey: string) => {
  if (!currentQuestion.value || isCurrentQuestionConfirmed.value)
    return

  const questionId = currentQuestion.value.id.toString()

  if (currentQuestion.value.type === 'MULTIPLE') {
    // 多选题
    if (!userAnswers.value[questionId]) {
      userAnswers.value[questionId] = []
    }
    const answers = userAnswers.value[questionId] as string[]
    const index = answers.indexOf(optionKey)
    if (index > -1) {
      answers.splice(index, 1)
    }
    else {
      answers.push(optionKey)
    }
  }
  else {
    // 单选题
    userAnswers.value[questionId] = optionKey
  }
}

// 确认答案
const confirmAnswer = () => {
  if (!currentQuestion.value)
    return

  const questionId = currentQuestion.value.id.toString()
  const answer = userAnswers.value[questionId]

  // 检查是否已选择答案
  if (!answer || (Array.isArray(answer) && answer.length === 0)) {
    toast.warning('请先选择答案')
    return
  }

  // 确认答案并显示解析
  confirmedQuestions.value.add(questionId)
  showAnalysis.value = true

  // 更新进度
  saveProgress()
}

// 检查选项是否被选中
const isOptionSelected = (optionKey: string) => {
  if (!currentQuestion.value)
    return false

  const questionId = currentQuestion.value.id.toString()
  const answer = userAnswers.value[questionId]

  if (currentQuestion.value.type === 'MULTIPLE') {
    return Array.isArray(answer) && answer.includes(optionKey)
  }
  else {
    return answer === optionKey
  }
}

// 上一题
const previousQuestion = () => {
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--
    saveProgress()
  }
}

// 下一题
const nextQuestion = () => {
  if (!practiceDetail.value || !currentQuestion.value)
    return

  // 如果当前题目未被确认，则先确认答案
  if (!isCurrentQuestionConfirmed.value) {
    const questionId = currentQuestion.value.id.toString()
    const answer = userAnswers.value[questionId]

    if (!answer || (Array.isArray(answer) && answer.length === 0)) {
      toast.warning('请先选择答案')
      return
    }
    // 确认答案
    confirmedQuestions.value.add(questionId)
  }

  // 切换到下一题或提交
  if (currentQuestionIndex.value < practiceDetail.value.questions.length - 1) {
    currentQuestionIndex.value++
    saveProgress()
  }
  else {
    if (practiceDetail.value.status === 'COMPLETED') {
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
    else {
      // 最后一题，提交答案
      submitAnswers()
    }
  }
}

// 跳转到指定题目
const jumpToQuestion = (index: number) => {
  currentQuestionIndex.value = index
}

// 检查题目是否已答
const isQuestionAnswered = (index: number) => {
  if (!practiceDetail.value?.questions)
    return false
  const question = practiceDetail.value.questions[index]
  const questionId = question.id.toString()
  const answer = userAnswers.value[questionId]

  if (question.type === 'MULTIPLE') {
    return Array.isArray(answer) && answer.length > 0
  }
  else {
    return answer !== undefined && answer !== null && answer !== ''
  }
}

// 检查已回答的题目是否回答正确
const isQuestionAnsweredCorrectly = (index: number) => {
  if (!practiceDetail.value?.questions)
    return false
  const question = practiceDetail.value.questions[index]

  // 只有已回答且已确认的题目才能判断正确性
  if (!isQuestionAnswered(index))
    return false
  const questionId = question.id.toString()
  if (!confirmedQuestions.value.has(questionId))
    return false

  return isUserAnswerCorrect(question)
}

// 检查已回答的题目是否回答错误
const isQuestionAnsweredWrongly = (index: number) => {
  if (!practiceDetail.value?.questions)
    return false
  const question = practiceDetail.value.questions[index]

  // 只有已回答且已确认的题目才能判断错误性
  if (!isQuestionAnswered(index))
    return false
  const questionId = question.id.toString()
  if (!confirmedQuestions.value.has(questionId))
    return false

  return !isUserAnswerCorrect(question)
}

// 保存答题进度
const saveProgress = async () => {
  if (!practiceId.value)
    return

  try {
    await updatePracticeProgress({
      practiceId: practiceId.value,
      answerData: Object.keys(userAnswers.value).reduce((acc: Record<string, string>, key) => {
        const answer = userAnswers.value[key]
        if (Array.isArray(answer)) {
          acc[key] = answer.join(',')
        }
        else {
          acc[key] = answer
        }
        return acc
      }, {}),
    })
  }
  catch (error) {
    console.error('保存进度失败:', error)
  }
}

// 提交答案
const submitAnswers = async () => {
  if (answeredCount.value !== practiceDetail.value?.questions.length) {
    toast.warning('请完成所有题目后再提交')
    return
  }
  if (submitting.value)
    return
  message
    .confirm({
      msg: '确定要提交吗？提交后练习将结束，再练习该题库需要重新生成。',
      title: '确认提交',
    })
    .then(async () => {
      submitting.value = true
      try {
        await saveProgress()
        await completePractice({ practiceId: practiceId.value! })
        redirectTo({
          url: `/pages/training-center/practice/complete?practiceId=${practiceId.value}`,
        })
      }
      catch (error) {
        console.error('提交练习失败:', error)
        toast.error('提交失败，请重试')
      }
      finally {
        submitting.value = false
      }
    })
    .catch(() => {})
}

// 显示题目列表弹窗
const showQuestionList = ref(false)

const toggleQuestionList = () => {
  showQuestionList.value = !showQuestionList.value
}

// 返回上一页
const handleGoBack = () => {
  uni.navigateBack()
}

// 检查用户答案是否正确
const isUserAnswerCorrect = (question: any) => {
  if (!question || !question.answer)
    return false

  const questionId = question.id.toString()
  const userAnswer = userAnswers.value[questionId]

  if (!userAnswer)
    return false

  if (question.type === 'MULTIPLE_CHOICE' || question.type === 'MULTIPLE') {
    // 多选题：比较排序后的答案
    if (!Array.isArray(userAnswer))
      return false
    const sortedUserAnswer = userAnswer.sort().join(',')
    const sortedCorrectAnswer = question.answer.split(',').sort().join(',')
    return sortedUserAnswer === sortedCorrectAnswer
  }
  else {
    // 单选题：直接比较
    return userAnswer === question.answer
  }
}

// 切换题目时根据确认状态决定是否显示解析
watch(currentQuestionIndex, () => {
  if (currentQuestion.value) {
    const questionId = currentQuestion.value.id.toString()
    showAnalysis.value = confirmedQuestions.value.has(questionId)
  }
  else {
    showAnalysis.value = false
  }
})

const handleSelectQuestion = (index: number) => {
  jumpToQuestion(index)
  toggleQuestionList()
}
</script>

<template>
  <view class="practice-page">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 练习内容 -->
    <view v-else-if="practiceDetail && currentQuestion" class="practice-content">
      <!-- 进度信息 -->
      <view class="progress-section">
        <view class="progress-info">
          <view class="question-count">
            <text class="label">题目：</text>
            <text class="value">{{ currentQuestionIndex + 1 }}/{{ practiceDetail.questions.length }}</text>
          </view>
        </view>
        <view class="progress-bar">
          <view
            class="progress-fill"
            :style="{ width: `${(answeredCount / practiceDetail.questions.length) * 100}%` }"
          ></view>
        </view>
      </view>

      <!-- 题目卡片 -->
      <view class="question-card">
        <view class="question-header">
          <text>问题 {{ currentQuestionIndex + 1 }}：{{
            getQuestionTypeText(currentQuestion.type)
          }}</text>
        </view>

        <view class="question-content">
          <text>{{ currentQuestion.title }}</text>
        </view>

        <view class="options-list">
          <view
            v-for="option in currentQuestion?.options || []"
            :key="option.id"
            class="option"
            :class="{
              selected: isOptionSelected(option.optionKey),
              disabled: isCurrentQuestionConfirmed,
            }"
            @click="selectAnswer(option.optionKey)"
          >
            <view
              class="option-indicator"
              :class="{ selected: isOptionSelected(option.optionKey) }"
            >
              <text>{{ option.optionKey }}</text>
            </view>
            <text class="option-text">{{ option.content }}</text>
          </view>
        </view>

        <!-- 确认答案按钮 -->
        <view v-if="!isCurrentQuestionConfirmed" class="confirm-section">
          <wd-button custom-class="w-full" type="primary" :round="false" @click="confirmAnswer">
            查看答案
          </wd-button>
        </view>

        <!-- 答案解析 -->
        <view v-if="showAnalysis" class="answer-analysis">
          <!-- 答题结果反馈 -->
          <view
            class="feedback-badge"
            :class="{
              'feedback-correct': isUserAnswerCorrect(currentQuestion),
              'feedback-wrong': !isUserAnswerCorrect(currentQuestion),
            }"
          >
            <text class="feedback-icon">
              {{ isUserAnswerCorrect(currentQuestion) ? '✓' : '✗' }}
            </text>
            <text class="feedback-text">
              {{ isUserAnswerCorrect(currentQuestion) ? '回答正确' : '回答错误' }}
            </text>
          </view>

          <text class="analysis-title">答案解析</text>
          <text class="analysis-content">正确答案：{{ currentQuestion.answer }}</text>
          <text v-if="currentQuestion.analysis" class="analysis-detail">{{
            currentQuestion.analysis
          }}</text>
        </view>
      </view>

      <!-- 底部导航 -->
      <view class="navigation-bar">
        <wd-button
          type="primary"
          :round="false"
          icon="arrow-left"
          custom-class="nav-button"
          :disabled="currentQuestionIndex === 0"
          @click="previousQuestion"
        >
          上一题
        </wd-button>
        <wd-button plain :round="false" custom-class="nav-button" @click="toggleQuestionList">
          题目列表
        </wd-button>
        <wd-button
          type="primary"
          :round="false"
          custom-class="nav-button"
          use-default-slot
          :disabled="submitting && currentQuestionIndex === practiceDetail.questions.length - 1"
          @click="nextQuestion"
        >
          <view class="btn-next-content">
            <text v-if="practiceDetail.status === 'COMPLETED' && currentQuestionIndex === practiceDetail.questions.length - 1">返回</text>
            <text v-else>{{
              currentQuestionIndex === practiceDetail.questions.length - 1 ? '提交' : '下一题'
            }}</text>
            <wd-icon
              v-if="currentQuestionIndex < practiceDetail.questions.length - 1"
              name="arrow-right"
              custom-class="right-icon"
            />
          </view>
        </wd-button>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else-if="!loading" class="empty-container">
      <text class="empty-text">练习内容不存在</text>
      <text class="empty-desc">请检查练习ID是否正确或联系管理员</text>
      <view class="empty-actions">
        <button class="back-btn" @click="handleGoBack">返回</button>
      </view>
    </view>

    <!-- 题目列表弹窗 -->
    <view v-if="showQuestionList" class="modal-overlay" @click="toggleQuestionList">
      <view class="modal" @click.stop>
        <view class="modal__header">
          <text class="modal__title">选择题目</text>
          <view class="modal__close" @click="toggleQuestionList">
            <text>×</text>
          </view>
        </view>

        <view class="modal__body">
          <!-- 状态说明 -->
          <view class="status-legend">
            <text class="legend__title">状态说明</text>
            <view class="legend__items">
              <view class="legend__item">
                <view class="legend__indicator legend__indicator--correct"></view>
                <text class="legend__text">回答正确</text>
              </view>
              <view class="legend__item">
                <view class="legend__indicator legend__indicator--wrong"></view>
                <text class="legend__text">回答错误</text>
              </view>
              <!-- <view class="legend__item">
                <view class="legend__indicator legend__indicator--current"></view>
                <text class="legend__text">当前题</text>
              </view> -->
            </view>
          </view>

          <!-- 全部题目 -->
          <view class="questions-section">
            <text class="section__title">全部题目</text>
            <view class="questions-grid">
              <view
                v-for="(question, index) in practiceDetail?.questions || []"
                :key="question.id"
                class="question-grid-item"
                :class="{
                  'question-grid-item--correct': isQuestionAnsweredCorrectly(index),
                  'question-grid-item--wrong': isQuestionAnsweredWrongly(index),
                  // 'question-grid-item--current': index === currentQuestionIndex,
                }"
                @click="handleSelectQuestion(index)"
              >
                <text>{{ index + 1 }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "练习"
  }
}
</route>

<style scoped lang="scss">
// 设计令牌
$colors: (
  primary: #1677ff,
  primary-light: #f0f7ff,
  secondary: #37acfe,
  success: #52c41a,
  success-light: #f6ffed,
  error: #f5222d,
  error-light: #fff2f0,
  warning: #faad14,
  warning-light: #fffbe6,
  text-primary: #333,
  text-secondary: #666,
  text-tertiary: #999,
  bg-page: #f7f7f5,
  bg-card: #fff,
  bg-light: #f5f7fa,
  border-light: #e8e8e8,
  border-medium: #d9d9d9,
  border-primary: #d6e8ff,
  white: #fff,
);

@function color($name) {
  @return map-get($colors, $name);
}

// 基础布局
.practice-page {
  min-height: 100vh;
  padding-bottom: 95px;
  background-color: color(bg-page);
}

.practice-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: color(text-secondary);
}

.loading-text {
  font-size: 14px;
  color: color(text-secondary);
}

// 进度区域
.progress-section {
  padding: 12px 18px;
  background-color: color(bg-card);
}

.progress-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 13px;
  color: color(text-secondary);
}

.question-count {
  display: flex;
  gap: 4px;
  align-items: center;
}

.label {
  color: color(text-secondary);
}

.value {
  color: color(primary);
}

.progress-bar {
  height: 2px;
  overflow: hidden;
  background-color: color(primary-light);
  border-radius: 1px;
}

.progress-fill {
  height: 100%;
  background-color: color(primary);
  border-radius: 1px;
  transition: width 0.3s ease;
}

// 题目卡片
.question-card {
  margin: 0 16px;
  overflow: hidden;
  background-color: color(bg-card);
  border-radius: 8px;
}

.question-header {
  padding: 8px 16px;
  font-size: 15px;
  font-weight: 500;
  color: color(primary);
  background-color: color(primary-light);
}

.question-content {
  padding: 15px 16px;
  font-size: 15px;
  line-height: 1.5;
  color: color(text-primary);
}

.options-list {
  padding: 0 16px 20px;
}

.option {
  display: flex;
  align-items: center;
  padding: 10px;
  margin-bottom: 10px;
  cursor: pointer;
  background-color: color(bg-card);
  border: 1px solid color(border-light);
  border-radius: 6px;
  transition: all 0.2s ease;

  &.selected {
    background-color: color(primary-light);
    border-color: color(primary);
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  &:hover:not(.disabled) {
    border-color: color(primary);
  }
}

.option-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  margin-right: 10px;
  font-size: 14px;
  color: color(text-secondary);
  border: 1px solid color(border-medium);
  border-radius: 50%;
  transition: all 0.2s ease;

  &.selected {
    color: color(primary);
    background-color: color(bg-card);
    border-color: color(primary);
  }
}

.option-text {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
  color: color(text-primary);
}

// 确认答案按钮
.confirm-section {
  display: flex;
  justify-content: center;
  padding: 0 16px 20px;
}

// 答案解析
.answer-analysis {
  position: relative;
  padding: 15px;
  margin: 0 16px 20px;
  background-color: color(primary-light);
  border-radius: 6px;
}

.feedback-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 10;
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 8px 12px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
}

.feedback-correct {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

.feedback-wrong {
  background-color: #fff1f0;
  border: 1px solid #ffa39e;
}

.feedback-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  font-size: 14px;
  font-weight: bold;
  border-radius: 50%;
}

.feedback-correct .feedback-icon {
  color: #fff;
  background-color: #52c41a;
}

.feedback-wrong .feedback-icon {
  color: #fff;
  background-color: #ff4d4f;
}

.feedback-text {
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
}

.feedback-correct .feedback-text {
  color: #52c41a;
}

.feedback-wrong .feedback-text {
  color: #ff4d4f;
}

.analysis-title {
  display: block;
  margin-bottom: 10px;
  font-size: 15px;
  font-weight: 500;
  color: color(primary);
}

.analysis-content,
.analysis-detail {
  display: block;
  font-size: 14px;
  line-height: 1.5;
  color: color(text-primary);
}

.analysis-content {
  margin-bottom: 5px;
}

// 导航栏
.navigation-bar {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: space-around;
  padding: 12px 16px;
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
  background-color: color(bg-card);
  border-top: 1px solid #f4f4f4;

  :deep(.nav-button) {
    flex: 1;
    margin: 0;
  }
}

.btn-next-content {
  display: flex;
  align-items: center;
  justify-content: center;

  .right-icon {
    margin-left: 4px;
    font-size: 14px;
    color: color(white);
  }
}

// 弹窗样式
.modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(0 0 0 / 50%);
}

.modal {
  width: 90%;
  max-width: 400px;
  max-height: 80vh;
  overflow: hidden;
  background-color: color(bg-card);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgb(0 0 0 / 10%);

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid color(border-light);
  }

  &__title {
    font-size: 18px;
    font-weight: 600;
    color: color(text-primary);
  }

  &__close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    font-size: 20px;
    color: color(text-secondary);
    cursor: pointer;
    transition: color 0.2s ease;

    &:hover {
      color: color(text-primary);
    }
  }

  &__body {
    max-height: calc(80vh - 80px);
    padding: 20px;
    overflow-y: auto;
  }
}

.status-legend {
  margin-bottom: 24px;
}

.legend {
  &__title {
    display: block;
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
    color: color(text-primary);
  }

  &__items {
    display: flex;
    gap: 20px;
  }

  &__item {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  &__indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;

    &--correct {
      background-color: color(success);
      border: 2px solid color(success);
    }

    &--wrong {
      background-color: color(error);
      border: 2px solid color(error);
    }

    &--current {
      background-color: color(primary);
      border: 2px solid color(primary);
    }
  }

  &__text {
    font-size: 14px;
    color: color(text-secondary);
  }
}

.questions-section {
  .section__title {
    display: block;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    color: color(text-primary);
  }
}

.questions-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
}

.question-grid-item {
  position: relative;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  overflow: hidden;
  font-size: 16px;
  font-weight: 500;
  color: color(text-secondary);
  cursor: pointer;
  background-color: color(bg-card);
  border: 2px solid color(border-medium);
  border-radius: 8px;
  transition: all 0.2s ease;

  // &::before {
  //   content: '';
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   width: 6px;
  //   height: 100%;
  //   background-color: transparent;
  //   transition: background-color 0.2s ease;
  //   z-index: 1;
  // }

  &--correct {
    color: color(success);
    background-color: color(success-light);
    border-color: color(success);

    // &::before {
    //   background-color: color(success);
    // }

    .question-number {
      color: color(success);
    }
  }

  &--wrong {
    color: color(error);
    background-color: color(error-light);
    border-color: color(error);
  }

  &--incorrect {
    color: color(danger);
    background-color: rgb(238 10 36 / 10%);
    border-color: color(danger);

    // &::before {
    //   background-color: color(danger);
    // }

    .question-number {
      color: color(danger);
    }
  }

  &--current {
    color: color(primary);
    background-color: color(primary-light);
    border-color: color(primary);
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    transform: scale(1.05);

    .question-number {
      font-weight: bold;
    }
  }

  .question-number {
    position: relative;
    z-index: 2;
  }

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    transform: scale(1.05);
  }
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 20px;
  color: color(text-secondary);
}

.empty-text {
  margin-bottom: 8px;
  font-size: 16px;
  color: color(text-secondary);
}

.empty-desc {
  margin-bottom: 20px;
  font-size: 14px;
  color: color(text-tertiary);
  text-align: center;
}

.empty-actions {
  display: flex;
  justify-content: center;
}

.back-btn {
  padding: 8px 16px;
  font-size: 14px;
  color: color(white);
  background-color: color(primary);
  border: none;
  border-radius: 6px;

  &::after {
    border: none;
  }
}
</style>
