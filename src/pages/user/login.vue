<script setup lang="ts">
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types'
import { getAppUserInfo, login } from '@/api'
import { useAppStore, useAuthStore, useUserStore } from '@/store'
import { navigateTo } from '@uni-helper/uni-promises'
import { storeToRefs } from 'pinia'
import { onMounted, ref } from 'vue'
import PrivacyAgreement from './components/PrivacyAgreement.vue'

defineOptions({
  name: 'LoginPage',
})

const authStore = useAuthStore()
const userStore = useUserStore()
const appStore = useAppStore()
const { isPrivacyAgreed } = storeToRefs(appStore)

const activeTab = ref(0)
const formData = ref({
  username: '15251083185',
  password: 'hAIER123456',
})
const formRef = ref<FormInstance>()

// 隐私协议弹窗控制
const showPrivacyDialog = ref(false)

const rules: FormRules = {
  username: [{ required: true, message: '请输入账号' }],
  password: [{ required: true, message: '请输入密码' }],
}

// 页面加载时检查隐私协议
onMounted(() => {
  // 如果用户未同意隐私协议，直接弹窗
  if (!isPrivacyAgreed.value) {
    showPrivacyDialog.value = true
  }
})

async function handleLogin() {
  if (!formRef.value)
    return

  try {
    const { valid } = await formRef.value.validate()
    if (valid) {
      // 检查用户是否已同意隐私协议
      if (!isPrivacyAgreed.value) {
        showPrivacyDialog.value = true
        return
      }

      // 用户已同意隐私协议，继续登录流程
      doLogin()
    }
  }
  catch (error) {
    console.error('Validation error:', error)
    uni.showToast({ title: '表单校验失败', icon: 'none' })
  }
}

function doLogin() {
  const userType = activeTab.value === 0 ? 'haier' : 'merchant'
  login({
    username: formData.value.username,
    password: formData.value.password,
    userType,
  })
    .then(async (res) => {
      authStore.setToken(res.access_token)
      const userInfo = await getAppUserInfo(userType)
      userStore.setUserInfo({
        ...userInfo,
        userType,
      })
      uni.showToast({ title: '登录成功' })
      uni.switchTab({ url: '/pages/index/index' })
    })
    .catch(() => {
      uni.showToast({ title: '登录失败', icon: 'none' })
    })
}

function goToRegister() {
  uni.navigateTo({
    url: '/pages/user/register',
  })
}

function goToForgotPassword() {
  navigateTo({
    url: '/pages/user/forget',
  })
}

function goToAgreement(type: 'service' | 'privacy') {
  uni.showToast({ title: `查看${type === 'service' ? '服务协议' : '隐私政策'}`, icon: 'none' })
}

// 处理隐私协议同意
function handleAgreePrivacy() {
  appStore.setPrivacyAgreed(true)
  showPrivacyDialog.value = false
  // 不自动登录，让用户主动点击登录按钮
}

// 处理隐私协议不同意
function handleDisagreePrivacy() {
  showPrivacyDialog.value = false
  uni.showToast({ title: '需要同意隐私协议才能使用本应用', icon: 'none' })
}
</script>

<template>
  <view class="login-page h-screen flex flex-col overflow-hidden">
    <view class="login-page__header relative h-300px">
      <view class="bg absolute inset-0 z-0" />
      <view class="logo-container absolute left-30px top-113px z-1">
        <image src="/static/logo.png" class="logo h-72px w-72px rounded-17px" mode="aspectFit" />
      </view>
    </view>

    <view class="login-page__tabs mb-8 flex items-center px-30px">
      <view
        class="tab-item mr-30px"
        :class="{ 'tab-item--active': activeTab === 0 }"
        @click="activeTab = 0"
      >
        <text class="text-16px font-medium"> 海尔用户 </text>
      </view>
      <view
        class="tab-item"
        :class="{ 'tab-item--active': activeTab === 1 }"
        @click="activeTab = 1"
      >
        <text class="text-16px font-medium"> 运维商用户 </text>
      </view>
    </view>

    <view class="login-page__form flex-1 px-30px">
      <wd-form ref="formRef" :model="formData" :rules="rules">
        <wd-cell-group border>
          <wd-input
            v-model="formData.username"
            prop="username"
            label-width="0"
            placeholder="请输入账号"
            clearable
            size="large"
            placeholder-style="font-size: 15px; color: #8F959E;"
            input-style="font-size: 15px;"
          />
          <wd-input
            v-model="formData.password"
            prop="password"
            label-width="0"
            placeholder="请输入密码"
            clearable
            show-password
            size="large"
            placeholder-style="font-size: 15px; color: #8F959E;"
            input-style="font-size: 15px;"
          />
        </wd-cell-group>
      </wd-form>

      <view
        :style="{ visibility: activeTab === 1 ? 'visible' : 'hidden' }"
        class="mt-15px flex justify-end"
      >
        <text class="text-14px text-[#8F959E]" @click="goToForgotPassword"> 忘记密码 </text>
      </view>

      <wd-button
        type="primary"
        block
        size="large"
        :round="false"
        custom-class="mt-40px !h-50px"
        @click="handleLogin"
      >
        登录
      </wd-button>

      <view v-if="activeTab === 1" class="mt-25px text-center">
        <text class="text-14px text-[#8F959E]"> 没有账号？ </text>
        <text class="text-14px text-primary" @click="goToRegister"> 立即注册 </text>
      </view>
    </view>

    <view class="login-page__agreement py-20px text-center text-14px">
      <text class="text-[#8F959E]"> 登录代表您已同意 </text>
      <text class="text-primary" @click="goToAgreement('service')"> 服务协议 </text>
      <text class="text-[#8F959E]"> 和 </text>
      <text class="text-primary" @click="goToAgreement('privacy')"> 隐私政策 </text>
    </view>

    <!-- 隐私协议弹窗 -->
    <PrivacyAgreement
      v-model:visible="showPrivacyDialog"
      @agree="handleAgreePrivacy"
      @disagree="handleDisagreePrivacy"
    />
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "登录"
  }
}
</route>

<style scoped lang="scss">
.login-page {
  background-color: white;

  &__header {
    .bg {
      background: linear-gradient(103deg, #ffede5 0%, #b9daff 81%);

      &::after {
        position: absolute;
        inset: 0;
        content: '';
        background: linear-gradient(180deg, rgb(255 255 255 / 0.01%) 0%, #fff 95%);
      }
    }
  }

  &__tabs {
    .tab-item {
      padding-bottom: 8px;
      color: #8f959e;
      border-bottom: 2px solid transparent;
      transition: all 0.3s ease;

      &--active {
        color: #2f2f2f;
        border-bottom-color: #2887ff;
      }
    }
  }

  &__form {
    :deep(.wd-input) {
      padding: 15px 0;
      background-color: transparent;
    }

    :deep(.wd-cell-group) {
      background-color: transparent;

      &::after {
        border: none;
      }
    }

    :deep(.wd-cell) {
      padding: 0 !important;
      margin: 0 !important;
      background-color: transparent !important;

      &::after {
        left: 0 !important;
        border-bottom: 1px solid #d0d3d5;
      }
    }

    :deep(.wd-input__label) {
      display: none;
    }
  }

  &__agreement {
    .text-primary {
      color: #2887ff;
    }
  }
}
</style>
