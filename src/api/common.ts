import type { VersionInformation } from '@/types/api/App'
import type { Option } from '@/types/common'
import { merchantInstance } from '@/service'

/**
 * @summary 获取省市区列表
 * @description 获取省市区列表
 */
export async function getRegionList(): Promise<Record<string, any>> {
  return await merchantInstance.get('/member/address/regionList.do')
}

/**
 * @summary 获取天气
 * @description 获取天气
 */
export async function getWeather(params: {
  lat: number
  lng: number
}): Promise<Record<string, any>> {
  return await merchantInstance.get('/lightOperationProvider/weatherByCoordinates.do', { params })
}

/**
 * @summary 获取版本列表
 * @description 获取版本列表
 */
export async function getVersionList(params: {
  // APP类型(Android,IOS)
  platformCode: string
  // 版本类型(1 - 正式版,2 - 测试版)
  versionType: number
}): Promise<VersionInformation[]> {
  return await merchantInstance.get('/light/lightVersionInformation/list.do', {
    params: { ...params, appType: 3 },
  })
}

/**
 * @summary 获取运维商字典
 * @description 获取运维商字典
 */
export async function getMerchantDict(dictType: string): Promise<Option[]> {
  return await merchantInstance.post('/repairs/cdDictData/getList', {
    dictType,
  })
}

/**
 * @summary 备件列表
 * @description 备件列表
 */
export async function getMaterial(data: any): Promise<any> {
  const { page, row, ...rest } = data
  return await merchantInstance.post('/repairs/cdMaterial/getPage', rest, {
    params: {
      page,
      row,
    },
  })
}
