import { merchantInstance } from '@/service'

/**
 * 地址相关接口
 */

/**
 * @summary 查询所有地址
 * @description 获取用户的所有收货地址列表
 */
export async function getAddressList(params: any = {}): Promise<any> {
  return await merchantInstance.post('/repairs/cdWarehouseAddress/getAllList', params)
}

/**
 * @summary 新增地址
 * @description 新增收货地址
 */
export async function addAddress(params: any): Promise<any> {
  return await merchantInstance.post('/repairs/cdWarehouseAddress/save', params)
}

/**
 * @summary 编辑地址加载
 * @description 根据ID获取地址详情用于编辑
 */
export async function getAddressById(id: string): Promise<any> {
  return await merchantInstance.get(`/repairs/cdWarehouseAddress/getInfoById?id=${id}`)
}

/**
 * @summary 设为默认地址
 * @description 设置指定地址为默认地址
 */
export async function setDefaultAddress(params: any): Promise<any> {
  return await merchantInstance.post('/repairs/cdWarehouseAddress/updateDefault', params)
}

/**
 * @summary 删除地址
 * @description 删除指定的收货地址
 */
export async function deleteAddress(params: any): Promise<any> {
  return await merchantInstance.post('/repairs/cdWarehouseAddress/delete', params)
}

/**
 * @summary 根据会员ID获取仓库和地址信息
 * @description 获取会员的默认仓库和收货地址信息
 */
export async function getCdWarehouseAndAddressByMemberID(memberId: number): Promise<any> {
  return await merchantInstance.get(`/repairs/cdWarehouseAddress/getCdWarehouseAndAddressByMemberID?type=3&memberId=${memberId}`)
}
