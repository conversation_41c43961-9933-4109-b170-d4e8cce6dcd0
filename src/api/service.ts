import type { PaginatedContent } from '@/service/types'
import type {
  ChatMessage,
  ChatResponse,
  ChatTemplate,
  ClearChatHistoryParams,
  ClientChatRequest,
  GetChatHistoryPageParams,
  GetChatHistoryParams,
  RegenerateAnswerParams,
  SseEmitter,
} from '@/types/api/Service'
import { getInstance } from '@/service'
import { useUserStore } from '@/store'

/**
 * 获取聊天记录
 * @param params
 */
export async function getChatHistory(params: GetChatHistoryParams): Promise<ChatMessage[]> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    haier: '/light/operation/ai/chat/history',
    merchant: '/light/operation/ai/chat/history',
  }
  const path = urlMap[userType!]
  return await getInstance().get(path, { params })
}

/**
 * 清除聊天记录
 * @param params
 */
export async function clearChatHistory(params: ClearChatHistoryParams): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    haier: '/light/operation/ai/chat/history/clear',
    merchant: '/light/operation/ai/chat/history/clear',
  }
  const path = urlMap[userType!]
  return await getInstance().post(path, undefined, { params })
}

/**
 * 分页获取聊天记录
 * @param params
 */
export async function getChatHistoryPage(
  params: GetChatHistoryPageParams,
): Promise<PaginatedContent<ChatMessage>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    haier: '/light/operation/ai/chat/history/page',
    merchant: '/light/operation/ai/chat/history/page',
  }
  const path = urlMap[userType!]
  return await getInstance().get(path, { params })
}

/**
 * 发送消息
 * @param data
 */
export async function sendMessage(data: ClientChatRequest): Promise<ChatResponse> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    haier: '/light/operation/ai/chat/message',
    merchant: '/light/operation/ai/chat/message',
  }
  const path = urlMap[userType!]
  return await getInstance().post(path, data)
}

/**
 * 重新生成答案
 * @param params
 */
export async function regenerateAnswer(params: RegenerateAnswerParams): Promise<SseEmitter> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    haier: '/light/operation/ai/chat/regenerate',
    merchant: '/light/operation/ai/chat/regenerate',
  }
  const path = urlMap[userType!]
  return await getInstance().post(path, undefined, { params })
}

/**
 * 获取聊天模板
 */
export async function getChatTemplates(): Promise<ChatTemplate[]> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    haier: '/light/operation/ai/chat/templates',
    merchant: '/light/operation/ai/chat/templates',
  }
  const path = urlMap[userType!]
  return await getInstance().get(path)
}
