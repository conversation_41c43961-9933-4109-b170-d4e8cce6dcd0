import type { PaginatedContent } from '@/service/types'
import type {
  AssignWorkOrderReq,
  BatchAuditResultDto,
  WorkOrder,
  WorkOrderAppealAuditReq,
  WorkOrderAppealInfo,
  WorkorderAppealListParams,
  WorkorderAppealParams,
  WorkOrderAppealReq,
  WorkorderBatchAuditPassParams,
  WorkorderBatchDispatchParams,
  WorkOrderCloseReq,
  WorkOrderConfig,
  WorkorderConfigPageParams,
  WorkOrderHandleReq,
  WorkOrderPageParams,
  WorkOrderRejectReq,
  WorkOrderSubmitReq,
} from '@/types/api/Workorder'
import { getInstance, hdsInstance, merchantInstance } from '@/service'
import { useUserStore } from '@/store'

export async function getWorkOrderPage(
  params: WorkOrderPageParams,
): Promise<PaginatedContent<WorkOrder>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '/light/operation/workOrder/page',
    haier: '/light/operation/workOrder/page',
  }
  const path = urlMap[userType]

  return await getInstance().get(path, { params })
}
export async function getWorkOrderToHandlePage(
  params: WorkOrderPageParams,
): Promise<PaginatedContent<WorkOrder>> {
  const userStore = useUserStore()
  const roleCode = userStore.userInfo!.roleCode
  const urlMap: Record<string, string> = {
    head_office: '/light/operation/workOrder/page',
    sub_center: '/light/operation/workOrder/page',
    light_osp: '/light/operation/workOrder/page',
    light_osp_staff: '/light/operation/app/workOrder/to-handle',
  }
  const path = urlMap[roleCode!]

  return await getInstance().get(path, { params })
}

export async function auditRejectWorkOrder(data: WorkOrderRejectReq): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '',
    haier: '/light/operation/workOrder/audit-reject',
  }
  const path = urlMap[userType]
  return await getInstance().post(path, data)
}

export async function batchAuditPassWorkOrder(
  data: WorkorderBatchAuditPassParams,
): Promise<BatchAuditResultDto> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '',
    haier: '/light/operation/workOrder/batch-audit-pass',
  }
  const path = urlMap[userType]
  return await getInstance().post(path, data)
}

export async function batchDispatchWorkOrder(
  data: WorkorderBatchDispatchParams,
): Promise<BatchAuditResultDto> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '',
    haier: '/light/operation/workOrder/batch-dispatch',
  }
  const path = urlMap[userType]
  return await getInstance().post(path, data)
}

export async function closeWorkOrder(data: WorkOrderCloseReq): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '/light/operation/workOrder/close',
    haier: '/light/operation/workOrder/close',
  }
  const path = urlMap[userType]
  return await getInstance().post(path, data)
}

export async function getWorkOrderByOrderCode(orderCode: string): Promise<WorkOrder> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: `/light/operation/workOrder/getByOrderCode/${orderCode}`,
    haier: `/light/operation/workOrder/getByOrderCode/${orderCode}`,
  }
  const path = urlMap[userType]
  return await getInstance().get(path)
}

export async function handleWorkOrder(data: WorkOrderHandleReq): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '/light/operation/workOrder/handle',
    haier: '/light/operation/workOrder/handle',
  }
  const path = urlMap[userType]
  return await getInstance().post(path, data)
}

export async function getWorkOrderHandledPage(
  params: WorkOrderPageParams,
): Promise<PaginatedContent<WorkOrder>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '/light/operation/workOrder/handled/page',
    haier: '/light/operation/workOrder/handled/page',
  }
  const path = urlMap[userType]
  return await getInstance().get(path, { params })
}

export async function getWorkOrderList(
  params: WorkOrderPageParams,
): Promise<PaginatedContent<WorkOrder>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '',
    haier: '/light/operation/workOrder/list',
  }
  const path = urlMap[userType]
  return await getInstance().get(path, { params })
}

export async function getWorkOrderMySubmitted(
  params: WorkOrderPageParams,
): Promise<PaginatedContent<WorkOrder>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '/light/operation/workOrder/my-submitted',
    haier: '/light/operation/workOrder/my-submitted',
  }
  const path = urlMap[userType]
  return await getInstance().get(path, { params })
}

export async function submitWorkOrder(data: WorkOrderSubmitReq): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '/light/operation/workOrder/submit',
    haier: '/light/operation/workOrder/submit',
  }
  const path = urlMap[userType]
  return await getInstance().post(path, data)
}

export async function getWorkOrderToAudit(
  params: WorkOrderPageParams,
): Promise<PaginatedContent<WorkOrder>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '',
    haier: '/light/operation/workOrder/to-audit',
  }
  const path = urlMap[userType]
  return await getInstance().get(path, { params })
}

export async function getWorkOrderToDispatch(
  params: WorkOrderPageParams,
): Promise<PaginatedContent<WorkOrder>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '',
    haier: '/light/operation/workOrder/to-dispatch',
  }
  const path = urlMap[userType]
  return await getInstance().get(path, { params })
}

export async function auditPassWorkOrder(orderCode: string): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '',
    haier: `/light/operation/workOrder/${orderCode}/audit-pass`,
  }
  const path = urlMap[userType]
  return await getInstance().post(path)
}

export async function assignWorkOrder(data: AssignWorkOrderReq): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '/light/operation/workOrder/assign',
    haier: '/light/operation/workOrder/assign',
  }
  const path = urlMap[userType]
  return await getInstance().post(path, data)
}

export async function getWorkOrderConfigList(
  params: WorkorderConfigPageParams,
): Promise<WorkOrderConfig[]> {
  const path = '/light/operation/work-order-config/list'
  return await hdsInstance.get(path, { params })
}

export async function dispatchWorkOrder(orderCode: string): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '',
    haier: `/light/operation/workOrder/${orderCode}/dispatch`,
  }
  const path = urlMap[userType]
  return await getInstance().post(path)
}

/**
 * 分页查询已审核的超期申诉工单列表
 */
export function getAuditedAppealWorkOrderByPage(
  params: WorkorderAppealParams,
): Promise<PaginatedContent<WorkOrder>> {
  return hdsInstance.get('/light/operation/workOrder/appeal/audited', { params })
}

/**
 * 根据工单编号查询申诉记录列表
 */
export function getAppealsByOrderCode(
  params: WorkorderAppealListParams,
): Promise<WorkOrderAppealInfo[]> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '/light/operation/workOrder/appeal/list',
    haier: '/light/operation/workOrder/appeal/list',
  }
  const path = urlMap[userType]
  return getInstance().get(path, { params })
}

/**
 * 分页查询待审核超期申诉的工单列表
 */
export function getPendingAppealWorkOrderByPage(
  params: WorkorderAppealParams,
): Promise<PaginatedContent<WorkOrder>> {
  return hdsInstance.get('/light/operation/workOrder/appeal/pending', { params })
}

/**
 * 分页查询待审核超期申诉的工单列表
 */
export function getMyAppealWorkOrderByPage(
  params: WorkorderAppealParams,
): Promise<PaginatedContent<WorkOrder>> {
  return merchantInstance.get('/light/operation/workOrder/appeal/my-submitted', { params })
}

/**
 * 分页查询待审核超期申诉的工单列表
 */
export function getMerchantAppealWorkOrderByPage(
  params: WorkorderAppealParams,
): Promise<PaginatedContent<WorkOrder>> {
  return merchantInstance.get('/light/operation/workOrder/appeal/op', { params })
}

/**
 * 审核工单超期申诉
 */
export function auditAppealWorkOrder(
  data: WorkOrderAppealAuditReq,
): Promise<boolean> {
  return hdsInstance.post('/light/operation/workOrder/appeal/audit', data)
}

/**
 * 审核工单超期申诉
 */
export function submitAppealWorkOrder(
  data: WorkOrderAppealReq,
): Promise<boolean> {
  return merchantInstance.post('/light/operation/workOrder/appeal/submit', data)
}

/**
 * 工单下的备件信息查询列表分页
 */
export function applyWorkorderSparePartsPage(
  data: any,
): Promise<any> {
  return merchantInstance.post('/repairs/woPart/orderWoPartPage', data)
}

/**
 * 工单下的备件信息查询列表分页和附件
 */
export function applyWorkorderSparePartsFile(
  data: any,
): Promise<any> {
  const { serviceInfoId, ...rest } = data
  return merchantInstance.post('/repairs/woPart/orderWoPartPageAndFile', {
    serviceInfoId,
  }, {
    params: { ...rest },
  })
}

/**
 * 工单备件新增附件
 */
export function saveWorkorderSparePartsFile(
  data: any,
): Promise<any> {
  return merchantInstance.post('/light/operation/wo-part/save-order-wo-part-file', data)
}

/**
 * 申请备件
 */
export function applyWorkorderSpareParts(
  data: any,
): Promise<any> {
  return merchantInstance.post('/light/operation/wo-part/apply-part', data)
}

/**
 * 备件登记校验
 */
export function checkWorkorderSparePartsRegistration(
  data: any,
): Promise<any> {
  return merchantInstance.post('/repairs/woPart/sparePartsRegistrationCheck', data)
}

/**
 * 备件登记-保存
 */
export function submitWorkorderSparePartsRegistration(
  data: any,
): Promise<any> {
  return merchantInstance.post('/repairs/woPart/sparePartsRegistration', data)
}

/**
 * 获取运维商首页数量
 */
export function getWorkorderProvideCount(): Promise<{
  // 待指派工单数量
  toAssignCount: number
  // 待处理工单数量
  toProcessCount: number
}> {
  return merchantInstance.get('/light/operation/app/workOrder/provider/count')
}

/**
 * 获取服务兵首页数量
 */
export function getWorkorderStaffCount(): Promise<{
  // 已逾期工单数量
  overtimeCount: number
  // 待处理工单数量
  toProcessCount: number
}> {
  return merchantInstance.get('/light/operation/app/workOrder/staff/count')
}

/**
 * 获取海尔员工首页数量
 */
export function getWorkordeHaierCount(): Promise<{
  // 待审核工单数量
  toAuditCount: number
  // 待下发工单数量
  toDispatchCount: number
}> {
  return hdsInstance.get('/light/operation/workOrder/count')
}
