import type { VueQueryPluginOptions } from '@tanstack/vue-query'
import type { UnConfigExtend, UnError, UnInstance, UnResponse } from '@uni-helper/uni-network'
import type { ApiResponse } from './types'

import { AuthBaseUrl, DefaultHeaders, HDSBaseUrl, MerchantBaseUrl } from '@/constants'
import { useAuthStore, useUserStore } from '@/store'

import { MutationCache, QueryCache, QueryClient } from '@tanstack/vue-query'
import un from '@uni-helper/uni-network'

import { showNetworkError } from './helper'

function createInstance(baseUrl: string) {
  const newInstance = un.create({
    baseUrl,
    timeout: 30_000,
  }) as UnInstance

  newInstance.interceptors.request.use((config: UnConfigExtend) => {
    const authStore = useAuthStore()
    config.headers = {
      ...DefaultHeaders,
      ...config.headers,
      Authorization: `Bearer ${authStore.token}`,
    }
    if (config.params && Object.keys(config.params).length > 0) {
      const newParams: Record<string, any> = {}
      for (const key in config.params) {
        if (
          Object.prototype.hasOwnProperty.call(config.params, key)
          && config.params[key] !== undefined
        ) {
          newParams[key] = config.params[key]
        }
      }
      config.params = {
        ...newParams,
      }
    }
    return config
  })

  newInstance.interceptors.response.use(
    (response: UnResponse): UnResponse | Promise<UnResponse> => {
      const resData = response.data as ApiResponse
      if (resData.success) {
        return resData.result
      }
      else if (resData.error === 'invalid_token') {
        uni.reLaunch({
          url: '/pages/user/login',
        })
        return Promise.reject(resData.error || '登录过期，请重新登录')
      }
      else {
        uni.showToast({
          title: resData.error || '请求失败',
          icon: 'none',
        })
        return Promise.reject(resData.error || '请求失败')
      }
    },
  )

  return newInstance
}

export const hdsInstance = createInstance(HDSBaseUrl)
export const merchantInstance = createInstance(MerchantBaseUrl)
export function getInstance() {
  const { userInfo } = useUserStore()
  if (userInfo?.userType === 'haier') {
    return hdsInstance
  }
  else {
    return merchantInstance
  }
}
const authInstance = un.create({
  baseUrl: AuthBaseUrl,
  timeout: 30_000,
}) as UnInstance
authInstance.interceptors.response.use(
  (response: UnResponse<any>): any => {
    return response.data
  },
  (error: UnError): Promise<UnError> => {
    const response = error.response
    let errorMsg = '登录请求失败'
    if (response?.status === 400) {
      errorMsg = (response.data as any)?.error_description || '用户名或密码错误'
      uni.showToast({
        title: errorMsg,
        icon: 'none',
      })
    }
    return Promise.reject(error)
  },
)

export { authInstance }

export const vueQueryClient = new QueryClient({
  queryCache: new QueryCache({
    onError: (error) => {
      if (un.isCancel(error))
        return
      showNetworkError({ error: error as IUnError })
    },
  }),
  mutationCache: new MutationCache({
    onError: (error) => {
      if (un.isCancel(error))
        return
      showNetworkError({ error: error as IUnError })
    },
  }),
})

export const vueQueryPluginOptions: VueQueryPluginOptions = {
  queryClient: vueQueryClient,
}
